# 🔧 Backend API Documentation

## 📋 概述

Jobs Application AI Agent 后端服务基于 FastAPI 构建，提供完整的 LinkedIn 自动化、简历优化和求职信生成功能。

## 🚀 快速启动

### 启动后端服务
```bash
cd webui/backend
python main.py
```

**服务地址**: http://localhost:8003
**API文档**: http://localhost:8003/docs (Swagger UI)

## 📡 API 端点

### 🤖 LinkedIn 自动化 API

#### 搜索职位
```http
POST /search_jobs
Content-Type: application/json

{
  "keywords": "Python Developer",
  "location": "San Francisco",
  "max_pages": 5
}
```

#### 申请职位
```http
POST /apply_to_job
Content-Type: application/json

{
  "job_id": "12345",
  "job_url": "https://linkedin.com/jobs/view/12345"
}
```

#### 批量申请
```http
POST /batch_apply
Content-Type: application/json

{
  "job_ids": ["12345", "67890"],
  "max_applications": 10
}
```

#### 获取申请历史
```http
GET /application_history
```

#### 验证登录状态
```http
GET /verify_login
```

#### Chrome 清理
```http
POST /cleanup_chrome
```

### 🎨 简历优化 API

#### 上传简历
```http
POST /upload_resume
Content-Type: multipart/form-data

file: resume.pdf
```

#### 优化简历
```http
POST /optimize_resume
Content-Type: application/json

{
  "job_url": "https://linkedin.com/jobs/view/12345",
  "resume_data": {...}
}
```

#### 生成简历 PDF
```http
POST /generate_resume_pdf
Content-Type: application/json

{
  "resume_html": "<html>...</html>",
  "template": "professional"
}
```

### 💌 求职信生成 API

#### 生成求职信
```http
POST /generate_cover_letter
Content-Type: application/json

{
  "company_name": "Google",
  "position": "Software Engineer",
  "job_description": "...",
  "personal_summary": "..."
}
```

#### 获取公司 Logo
```http
GET /company_logo/{company_name}
```

### ⚙️ 设置管理 API

#### 获取设置
```http
GET /settings
```

#### 更新设置
```http
POST /settings
Content-Type: application/json

{
  "automation_mode": "selenium",
  "max_applications": 50,
  "api_keys": {...}
}
```

### 📊 实时日志 WebSocket

#### 连接日志流
```javascript
const ws = new WebSocket('ws://localhost:8003/ws/logs');

ws.onmessage = (event) => {
  const logEntry = JSON.parse(event.data);
  console.log(logEntry);
};
```

## 🔧 配置文件

### 主配置 (config.py)
```python
# AI 模型配置
LLM_MODEL_TYPE = 'gemini'
LLM_MODEL = 'gemini-2.5-flash-preview-0514'
LLM_TEMPERATURE = 1.0
LLM_TIMEOUT = 60

# LinkedIn 自动化配置
LINKEDIN_AUTOMATION_MODE = 'selenium'
LINKEDIN_MAX_PAGES = 5
LINKEDIN_APPLICATION_TIMEOUT = 600

# 服务端口配置
FRONTEND_PORT = 3000
BACKEND_PORT = 8003
```

### API 密钥 (secrets.yaml)
```yaml
# Gemini API (推荐)
gemini_api_key: "your-gemini-api-key"

# OpenAI API (备选)
openai_api_key: "your-openai-api-key"

# LinkedIn 账户
linkedin_email: "<EMAIL>"
linkedin_password: "your-password"
```

## 🛠️ 技术架构

### 核心组件
- **FastAPI**: 现代 Web 框架
- **Selenium**: LinkedIn 自动化引擎
- **Gemini 2.5**: AI 内容生成
- **WebSocket**: 实时日志流
- **Pyppeteer**: PDF 生成

### 目录结构
```
webui/backend/
├── main.py              # FastAPI 主应用
├── linkedin_api.py      # LinkedIn API 路由
├── settings_api.py      # 设置管理 API
├── config/              # 配置文件目录
├── templates/           # 模板文件
├── utils/               # 工具函数
│   ├── browser_detector.py
│   └── __init__.py
└── log/                 # 日志文件
    ├── app.log
    ├── linkedin_automation.log
    └── selenium.log
```

## 📊 性能指标

### API 响应时间
- **搜索职位**: 30-60秒
- **申请职位**: 90-120秒
- **简历优化**: 30-60秒
- **求职信生成**: 20-40秒

### 并发处理
- **最大并发**: 10个请求
- **WebSocket 连接**: 无限制
- **内存使用**: 500MB-1GB

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口占用
netstat -ano | findstr :8003

# 杀死占用进程
taskkill /PID <PID> /F
```

#### 2. API 密钥错误
- 检查 `secrets.yaml` 配置
- 验证 API 密钥有效性
- 确认配额未超限

#### 3. LinkedIn 自动化失败
- 验证登录状态
- 执行 Chrome 清理
- 检查网络连接

#### 4. PDF 生成失败
- 确认 Chrome 浏览器已安装
- 检查 Pyppeteer 依赖
- 验证 HTML 内容格式

### 调试模式
```bash
# 启用调试日志
export LOG_LEVEL=DEBUG
python main.py
```

### 日志文件
- **应用日志**: `log/app.log`
- **LinkedIn 日志**: `log/linkedin_automation.log`
- **Selenium 日志**: `log/selenium.log`

## 🚀 部署指南

### 开发环境
```bash
# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
python main.py
```

### 生产环境
```bash
# 使用 Gunicorn
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app

# 使用 Docker
docker build -t aihawk-backend .
docker run -p 8003:8003 aihawk-backend
```

### 环境变量
```bash
# 生产环境配置
export ENVIRONMENT=production
export LOG_LEVEL=INFO
export CORS_ORIGINS=https://yourdomain.com
```

## 🔐 安全配置

### CORS 设置
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### API 密钥保护
- 使用环境变量存储敏感信息
- 定期轮换 API 密钥
- 监控 API 使用情况

---

**版本**: v2.0
**最后更新**: 2025-01-01
**状态**: ✅ 生产就绪

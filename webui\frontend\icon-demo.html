<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Material-UI Agent图标演示</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 40px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .icon-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .icon-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        .icon-display {
            font-size: 64px;
            color: #667eea;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .icon-card:hover .icon-display {
            transform: scale(1.1);
            color: #764ba2;
        }
        .icon-name {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .icon-code {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #495057;
            margin-bottom: 10px;
        }
        .icon-description {
            color: #666;
            font-size: 0.95em;
            line-height: 1.5;
        }
        .recommended {
            border: 3px solid #28a745;
            background: linear-gradient(135deg, #f8fff9, #e8f5e8);
        }
        .recommended::before {
            content: "🌟 推荐";
            position: absolute;
            top: -10px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .recommended {
            position: relative;
        }
        .usage-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-top: 40px;
        }
        .usage-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Material-UI Agent 图标演示</h1>
        
        <div class="icon-grid">
            <div class="icon-card recommended">
                <div class="icon-display material-icons">smart_toy</div>
                <div class="icon-name">Smart Toy</div>
                <div class="icon-code">smart_toy</div>
                <div class="icon-description">最适合AI Agent的图标，代表智能助手或机器人，现代且友好</div>
            </div>
            
            <div class="icon-card">
                <div class="icon-display material-icons">support_agent</div>
                <div class="icon-name">Support Agent</div>
                <div class="icon-code">support_agent</div>
                <div class="icon-description">专门用于代表客服或支持代理人员，适合客服场景</div>
            </div>
            
            <div class="icon-card">
                <div class="icon-display material-icons">android</div>
                <div class="icon-name">Android</div>
                <div class="icon-code">android</div>
                <div class="icon-description">安卓机器人图标，也可以用来代表AI助手</div>
            </div>
            
            <div class="icon-card">
                <div class="icon-display material-icons">assistant</div>
                <div class="icon-name">Assistant</div>
                <div class="icon-code">assistant</div>
                <div class="icon-description">通用的助手角色图标，简洁明了</div>
            </div>
            
            <div class="icon-card">
                <div class="icon-display material-icons">account_circle</div>
                <div class="icon-name">Account Circle</div>
                <div class="icon-code">account_circle</div>
                <div class="icon-description">可以代表用户代理或个人助手</div>
            </div>
            
            <div class="icon-card">
                <div class="icon-display material-icons">psychology</div>
                <div class="icon-name">Psychology</div>
                <div class="icon-code">psychology</div>
                <div class="icon-description">代表智能思考和AI分析能力</div>
            </div>
        </div>
        
        <div class="usage-section">
            <div class="usage-title">在React项目中的使用方法</div>
            
            <h3>1. 安装依赖</h3>
            <div class="code-block">npm install @mui/icons-material @mui/material @emotion/styled @emotion/react</div>
            
            <h3>2. 导入图标</h3>
            <div class="code-block">import SmartToyIcon from '@mui/icons-material/SmartToy';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import AndroidIcon from '@mui/icons-material/Android';
import AssistantIcon from '@mui/icons-material/Assistant';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import PsychologyIcon from '@mui/icons-material/Psychology';</div>
            
            <h3>3. 使用示例</h3>
            <div class="code-block">// 基本使用
&lt;SmartToyIcon /&gt;

// 自定义大小和颜色
&lt;SmartToyIcon fontSize="large" color="primary" /&gt;

// 自定义样式
&lt;SmartToyIcon sx={{ fontSize: 40, color: '#667eea' }} /&gt;</div>
            
            <h3>4. 在HTML中直接使用</h3>
            <div class="code-block">&lt;span class="material-icons"&gt;smart_toy&lt;/span&gt;
&lt;span class="material-icons"&gt;support_agent&lt;/span&gt;</div>
        </div>
    </div>
</body>
</html>
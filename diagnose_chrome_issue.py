#!/usr/bin/env python3
"""
Chrome和Undetected ChromeDriver问题诊断脚本
用于排查版本兼容性、权限问题等
"""

import os
import sys
import subprocess
import time
import shutil
from pathlib import Path
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_chrome_version():
    """获取本地Chrome版本"""
    try:
        # Windows
        if os.name == 'nt':
            import winreg
            try:
                # 尝试从注册表获取Chrome版本
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                version, _ = winreg.QueryValueEx(key, "version")
                winreg.CloseKey(key)
                return version
            except:
                pass
            
            # 尝试从文件系统获取
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]
            
            for path in chrome_paths:
                if os.path.exists(path):
                    try:
                        result = subprocess.run([path, '--version'], capture_output=True, text=True, timeout=5)
                        if result.returncode == 0:
                            import re
                            match = re.search(r'Chrome/(\d+\.\d+\.\d+\.\d+)', result.stdout)
                            if match:
                                return match.group(1)
                    except:
                        continue
        
        # Linux/Mac
        else:
            try:
                result = subprocess.run(['google-chrome', '--version'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    import re
                    match = re.search(r'Chrome/(\d+\.\d+\.\d+\.\d+)', result.stdout)
                    if match:
                        return match.group(1)
            except:
                pass
                
    except Exception as e:
        logger.warning(f"获取Chrome版本失败: {e}")
    
    return None

def check_chromedriver_cache():
    """检查ChromeDriver缓存"""
    cache_paths = []
    
    # WebDriver Manager缓存
    wdm_cache = Path.home() / ".wdm" / "drivers" / "chromedriver"
    if wdm_cache.exists():
        cache_paths.append(("WebDriver Manager", wdm_cache))
    
    # Undetected ChromeDriver缓存
    uc_cache = Path.home() / "appdata" / "roaming" / "undetected_chromedriver"
    if uc_cache.exists():
        cache_paths.append(("Undetected ChromeDriver", uc_cache))
    
    return cache_paths

def check_undetected_chromedriver():
    """检查undetected-chromedriver状态"""
    try:
        import undetected_chromedriver as uc
        logger.info(f"✅ undetected-chromedriver已安装，版本: {uc.__version__}")
        return True
    except ImportError:
        logger.error("❌ undetected-chromedriver未安装")
        return False
    except Exception as e:
        logger.error(f"❌ undetected-chromedriver检查失败: {e}")
        return False

def test_standard_selenium():
    """测试标准Selenium"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        logger.info("🧪 测试标准Selenium...")
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        driver.get("data:text/html,<html><body>Test</body></html>")
        title = driver.title
        driver.quit()
        
        logger.info("✅ 标准Selenium测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 标准Selenium测试失败: {e}")
        return False

def test_undetected_chromedriver():
    """测试Undetected ChromeDriver"""
    try:
        import undetected_chromedriver as uc
        
        logger.info("🧪 测试Undetected ChromeDriver...")
        
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        # 设置超时
        import signal
        def timeout_handler(signum, frame):
            raise TimeoutError("Undetected ChromeDriver初始化超时")
        
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(30)  # 30秒超时
        
        try:
            driver = uc.Chrome(options=options, version_main=None)  # 让它自动检测版本
            driver.get("data:text/html,<html><body>Test</body></html>")
            title = driver.title
            driver.quit()
            
            signal.alarm(0)  # 取消超时
            logger.info("✅ Undetected ChromeDriver测试成功")
            return True
            
        except TimeoutError:
            logger.error("❌ Undetected ChromeDriver初始化超时")
            return False
            
    except Exception as e:
        logger.error(f"❌ Undetected ChromeDriver测试失败: {e}")
        return False

def clean_chrome_cache():
    """清理Chrome相关缓存"""
    logger.info("🧹 清理Chrome相关缓存...")
    
    cleaned = []
    
    # 清理WebDriver Manager缓存
    wdm_cache = Path.home() / ".wdm"
    if wdm_cache.exists():
        try:
            shutil.rmtree(wdm_cache)
            cleaned.append("WebDriver Manager缓存")
        except Exception as e:
            logger.warning(f"清理WebDriver Manager缓存失败: {e}")
    
    # 清理Undetected ChromeDriver缓存
    uc_cache = Path.home() / "appdata" / "roaming" / "undetected_chromedriver"
    if uc_cache.exists():
        try:
            shutil.rmtree(uc_cache)
            cleaned.append("Undetected ChromeDriver缓存")
        except Exception as e:
            logger.warning(f"清理Undetected ChromeDriver缓存失败: {e}")
    
    # 清理项目Chrome配置
    project_chrome = Path.home() / ".linkedin_automation" / "chrome_profile"
    if project_chrome.exists():
        try:
            shutil.rmtree(project_chrome)
            cleaned.append("项目Chrome配置")
        except Exception as e:
            logger.warning(f"清理项目Chrome配置失败: {e}")
    
    if cleaned:
        logger.info(f"✅ 已清理: {', '.join(cleaned)}")
    else:
        logger.info("ℹ️ 没有找到需要清理的缓存")

def main():
    """主诊断流程"""
    logger.info("🔍 开始Chrome和Undetected ChromeDriver诊断...")
    
    # 1. 检查Chrome版本
    chrome_version = get_chrome_version()
    if chrome_version:
        logger.info(f"📋 Chrome版本: {chrome_version}")
        major_version = chrome_version.split('.')[0]
        logger.info(f"📋 Chrome主版本: {major_version}")
    else:
        logger.error("❌ 无法检测Chrome版本")
    
    # 2. 检查ChromeDriver缓存
    cache_paths = check_chromedriver_cache()
    if cache_paths:
        logger.info("📁 发现ChromeDriver缓存:")
        for name, path in cache_paths:
            logger.info(f"  - {name}: {path}")
    else:
        logger.info("📁 没有发现ChromeDriver缓存")
    
    # 3. 检查undetected-chromedriver
    uc_available = check_undetected_chromedriver()
    
    # 4. 测试标准Selenium
    selenium_ok = test_standard_selenium()
    
    # 5. 测试Undetected ChromeDriver
    if uc_available:
        uc_ok = test_undetected_chromedriver()
    else:
        uc_ok = False
    
    # 6. 生成诊断报告
    logger.info("\n" + "="*50)
    logger.info("📊 诊断报告")
    logger.info("="*50)
    logger.info(f"Chrome版本: {chrome_version or '未检测到'}")
    logger.info(f"标准Selenium: {'✅ 正常' if selenium_ok else '❌ 异常'}")
    logger.info(f"Undetected ChromeDriver: {'✅ 正常' if uc_ok else '❌ 异常'}")
    
    # 7. 提供解决方案
    if not uc_ok and uc_available:
        logger.info("\n🔧 建议的解决方案:")
        logger.info("1. 清理所有Chrome缓存")
        logger.info("2. 重新安装undetected-chromedriver")
        logger.info("3. 使用特定Chrome版本")
        
        user_input = input("\n是否要清理Chrome缓存? (y/n): ")
        if user_input.lower() == 'y':
            clean_chrome_cache()
            logger.info("✅ 缓存清理完成，请重新测试")

if __name__ == "__main__":
    main()

# 🎯 Jobot AIHawk / LinkedIn 职位申请自动化

<div align="center">

*AI-Powered Job Application Assistant - Accelerate Your Career with Intelligence*

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Node.js](https://img.shields.io/badge/Node.js-16+-green.svg)](https://nodejs.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![LinkedIn](https://img.shields.io/badge/LinkedIn-Automation-0077B5.svg)](https://linkedin.com)
[![Gemini](https://img.shields.io/badge/Powered%20by-Google%20Gemini%202.5-4285F4.svg)](https://ai.google.dev/)

**🚀 Intelligent Job Application Automation Platform powered by Google Gemini 2.5**

*Comprehensive job search solution integrating resume optimization, cover letter generation, and LinkedIn automation*

[🎯 Features](#-features) • [🚀 Quick Start](#-quick-start) • [📋 Installation](#-installation) • [📖 Usage Guide](#-usage-guide) • [⚙️ Configuration](#️-configuration) • [🔧 Troubleshooting](#-troubleshooting) • [🤝 Contributing](#-contributing)

</div>

---

## 🎯 Features

### 🎨 Smart Resume Optimization
- **🤖 AI-Driven Customization**: Powered by Google Gemini 2.5, intelligently analyzes job requirements and optimizes resume content
- **📄 PDF Upload Support**: Upload existing resumes for AI-powered analysis and targeted optimization
- **🎨 Professional Templates**: Multiple elegant resume templates for different industries
- **👀 Real-time Preview**: HTML preview with high-quality PDF export
- **🌍 Multi-language Support**: Perfect support for both English and Chinese resumes
- **✨ Smart Highlighting**: Highlights optimized content sections with visual indicators

### 💌 Intelligent Cover Letter Generation
- **🎯 Personalized Content**: Generate customized cover letters based on company information and job requirements
- **🏢 Company Logo Integration**: Automatically fetch target company logos for professional presentation
- **🌐 Bilingual Support**: Support for both English and Chinese cover letter generation
- **📊 Matching Analysis**: Visual charts showing candidate-position compatibility
- **🎨 Modern Templates**: Elegant design templates with multiple export formats

### 🤖 LinkedIn Automation
- **🔍 Smart Job Search**: Precise job search based on keywords, location, and other criteria
- **⚡ Batch Applications**: Support for bulk Easy Apply applications to boost efficiency
- **🔧 Multiple Automation Modes**:
  - 🚀 **Playwright Async** (Recommended) - Best performance and speed
  - 🛡️ **Playwright Sync** - Stable and reliable with good compatibility
  - 🔧 **Selenium** - Traditional solution with broad compatibility
- **🎯 Smart Filtering**: Automatically filter qualified positions and avoid duplicate applications
- **📊 Application Tracking**: Detailed records of application history, success rates, and status tracking
- **🔐 Secure Login**: Support for 2FA verification and CAPTCHA handling

### 🌐 Modern Web Interface
- **📱 Responsive Design**: Works seamlessly on desktop and mobile devices
- **🎨 Intuitive UI**: Clean, modern interface with smooth user experience
- **⚡ Real-time Updates**: Live status updates and progress tracking
- **🔄 Session Management**: Persistent data within browser sessions

## 🚀 Quick Start

### 📋 System Requirements
- 🐍 **Python 3.8+**
- 🟢 **Node.js 16+**
- 🌐 **Chrome Browser**
- 🔑 **Gemini API Key** (recommended) or OpenAI API Key

### ⚡ One-Command Setup

#### 1️⃣ Clone the Repository
```bash
git clone https://github.com/UJJacky/Jobs-Application_Linkedin_AIHawk.git
cd Jobs-Application_Linkedin_AIHawk
```

#### 2️⃣ Install Python Dependencies
```bash
# Create virtual environment
python -m venv virtual

# Activate virtual environment
virtual\Scripts\activate  # Windows
# source virtual/bin/activate  # Linux/Mac

# Install dependencies
pip install -r requirements.txt
```

#### 3️⃣ Install Frontend Dependencies
```bash
cd webui/frontend
npm install
cd ../..
```

#### 4️⃣ Configure API Keys
```bash
# Copy configuration templates
cp config.py.example config.py
cp data_folder/secrets.yaml.example data_folder/secrets.yaml

# Edit data_folder/secrets.yaml with your API keys
```

**secrets.yaml Configuration Example:**
```yaml
# Gemini API (Recommended - Better performance, lower cost)
gemini_api_key: "your-gemini-api-key-here"

# OpenAI API (Alternative)
openai_api_key: "your-openai-api-key-here"
```

### 🎬 Launch the Application

#### 🔧 Start Backend Service
```bash
cd webui/backend
python main.py
```
✅ Backend service runs on: `http://localhost:8003`

#### 🎨 Start Frontend Service
```bash
cd webui/frontend
npm start
```
✅ Frontend service runs on: `http://localhost:3000`

#### 🌐 Access the Application
Open your browser and visit: **http://localhost:3000**

## � Installation

### Standard Installation

#### Prerequisites
- Python 3.8 or higher
- Node.js 16 or higher
- Google Chrome browser
- At least 2GB available disk space

#### Core Dependencies
The project uses two main dependency files:
- `requirements.txt` - Core Python dependencies
- `requirements_linkedin.txt` - LinkedIn automation specific dependencies

#### Browser Automation Setup

**Playwright (Recommended)**
```bash
# Install Playwright
pip install playwright>=1.40.0

# Install browser binaries
python -m playwright install chromium
```

**Selenium (Alternative)**
```bash
# Selenium uses webdriver-manager for automatic ChromeDriver management
pip install selenium webdriver-manager
```

### Development Setup

#### Additional Development Dependencies
```bash
# Install development dependencies
pip install -r requirements.txt
pip install pytest black flake8  # Optional: for testing and code formatting
```

#### Environment Variables
```bash
# Set log level
export LOG_LEVEL=DEBUG

# Set proxy if needed
export HTTPS_PROXY=http://proxy:port
```

### Docker Setup (Optional)
```bash
# Build Docker image
docker build -t aihawk .

# Run container
docker run -p 3000:3000 -p 8003:8003 aihawk
```

## 📖 Usage Guide

### � Resume Generation and Optimization

1. **Upload Resume**: Click "Upload Resume" and select your PDF file
2. **Job URL Input**: Paste the target job posting URL
3. **AI Analysis**: The system analyzes job requirements and optimizes your resume
4. **Review Changes**: Review highlighted optimizations (marked with ✨)
5. **Export PDF**: Download the optimized resume as a professional PDF

### 💌 Cover Letter Creation

1. **Job Information**: Enter job URL or manually input company and position details
2. **Personal Summary**: Provide your professional summary and key qualifications
3. **Generate Content**: AI creates a personalized cover letter with company logo
4. **Matching Analysis**: View compatibility charts and analysis
5. **Export Options**: Download as PDF or copy HTML content

### 🤖 LinkedIn Automation

1. **Setup Automation**: Choose your preferred automation type (Playwright/Selenium)
2. **Login**: Enter LinkedIn credentials (supports 2FA)
3. **Search Configuration**: Set job search criteria (keywords, location, filters)
4. **Application Settings**: Configure application preferences and auto-responses
5. **Start Automation**: Begin automated job search and application process
6. **Monitor Progress**: Track applications in real-time with detailed logs

### ⚙️ Settings Management

Access the settings panel to configure:
- **Automation Preferences**: Browser settings, timeouts, retry counts
- **Application Limits**: Daily application limits and intervals
- **Data Management**: Backup, export, and import settings
- **UI Preferences**: Theme, language, and notification settings

## ⚙️ Configuration

### Basic Configuration

#### API Keys Setup
Edit `data_folder/secrets.yaml`:
```yaml
# Primary AI Provider (Choose one)
gemini_api_key: "your-gemini-api-key"
openai_api_key: "your-openai-api-key"

# LinkedIn Credentials
linkedin_email: "<EMAIL>"
linkedin_password: "your-password"
```

#### Application Settings
Edit `data_folder/work_preferences.yaml`:
```yaml
# Job Search Preferences
remote_work: true
willing_to_relocate: false
salary_range: [50000, 100000]
experience_level: "mid"

# Application Settings
max_applications_per_day: 50
application_delay: [30, 60]  # seconds
```

### Advanced Configuration

#### Browser Automation Settings
Edit `linkedin_config.yaml`:
```yaml
selenium:
  headless: false
  timeout: 30
  window_size: [1200, 800]

playwright:
  headless: false
  timeout: 30000
  browser_type: "chromium"
```

#### AI Model Configuration
Edit `config.py`:
```python
# AI Model Settings
LLM_MODEL = 'gemini-2.5-flash'
LLM_TEMPERATURE = 1.0
LLM_TIMEOUT = 60
LLM_MAX_RETRIES = 3
```

### Environment-Specific Settings

#### Development Environment
```bash
# Enable debug mode
export DEBUG=true
export LOG_LEVEL=DEBUG

# Use development ports
export FRONTEND_PORT=3000
export BACKEND_PORT=8003
```

#### Production Environment
```bash
# Production settings
export NODE_ENV=production
export LOG_LEVEL=INFO

# Security settings
export SECURE_COOKIES=true
export HTTPS_ONLY=true
```

## 🔧 Troubleshooting

### Common Installation Issues

#### Python Dependencies
```bash
# If pip install fails
pip install --upgrade pip
pip install --upgrade setuptools wheel

# For specific package issues
pip install --force-reinstall package-name
```

#### Node.js Dependencies
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Browser Automation Issues

#### Playwright Problems
```bash
# Reinstall browsers
python -m playwright uninstall
python -m playwright install chromium

# Check browser installation
python -m playwright install --help
```

#### Selenium ChromeDriver Issues
```bash
# Update webdriver-manager
pip install --upgrade webdriver-manager

# Clear webdriver cache
rm -rf ~/.wdm
```

### LinkedIn Automation Issues

#### Login Problems
- **2FA Required**: Disable headless mode to manually complete 2FA
- **CAPTCHA**: Use GUI mode and complete CAPTCHA manually
- **Rate Limiting**: Reduce application frequency and add delays

#### Application Failures
- **Review Button Not Found**: Check if LinkedIn UI has changed
- **Form Submission Errors**: Verify auto-answer settings
- **Session Timeout**: Increase timeout values in configuration

### PDF Generation Issues

#### Chrome CDP Problems
```bash
# Ensure Chrome is installed and accessible
google-chrome --version

# Check Chrome path in configuration
which google-chrome
```

#### Pyppeteer Fallback
```bash
# Install Pyppeteer dependencies
pip install pyppeteer

# Download Chromium for Pyppeteer
python -c "import pyppeteer; pyppeteer.chromium_downloader.download_chromium()"
```

### Network and Connection Issues

#### GitHub Connection Problems
```bash
# Use SSH instead of HTTPS
git remote set-<NAME_EMAIL>:UJJacky/Jobs-Application_Linkedin_AIHawk.git

# Configure proxy if needed
git config --global http.proxy http://proxy:port
```

#### API Connection Issues
- **Gemini API**: Check API key validity and quota
- **OpenAI API**: Verify API key and billing status
- **Rate Limits**: Implement exponential backoff

### Performance Issues

#### Memory Usage
```bash
# Monitor memory usage
htop  # Linux/Mac
taskmgr  # Windows

# Reduce browser instances
# Set headless: true in configuration
```

#### Slow Response Times
- **Increase timeouts** in configuration files
- **Reduce concurrent operations**
- **Check network connectivity**

### Getting Help

#### Log Files
- Backend logs: Console output or log files in `log/` directory
- Frontend logs: Browser developer tools console
- Automation logs: `log/selenium.log` or `log/playwright.log`

#### Debug Mode
```bash
# Enable verbose logging
export LOG_LEVEL=DEBUG
export DEBUG=true

# Run with debug output
python main.py --debug
```

#### Community Support
- **GitHub Issues**: Report bugs and request features
- **Discussions**: Ask questions and share experiences
- **Documentation**: Check this README and inline code comments
## 📁 Project Structure

```
Jobs-Application_Linkedin_AIHawk/
├── 📁 src/                          # Core source code
│   ├── 📁 libs/                     # Core libraries
│   │   ├── 📁 resume_and_cover_builder/  # Resume and cover letter generation
│   │   │   ├── 📁 llm/              # LLM-related modules
│   │   │   │   ├── 📄 llm_job_parser.py      # Job information parsing
│   │   │   │   ├── 📄 llm_generate_resume.py # Resume content generation
│   │   │   │   └── 📄 llm_generate_tailored_resume.py # Custom resume optimization
│   │   │   ├── 📁 resume_style/     # Resume style templates
│   │   │   │   ├── 📄 style_josylad_blue.css    # Blue theme template
│   │   │   │   ├── 📄 style_josylad_grey.css    # Grey theme template
│   │   │   │   └── 📄 style_krishnavalliappan.css # Professional template
│   │   │   ├── 📄 resume_facade.py  # Resume generation facade
│   │   │   ├── 📄 style_manager.py  # Style manager
│   │   │   └── 📄 resume_generator.py # Resume generator
│   │   └── 📄 llm_manager.py        # LLM unified manager
│   ├── 📁 utils/                    # Utility modules
│   │   ├── 📄 chrome_utils.py       # Chrome browser utilities
│   │   └── 📄 constants.py          # Constants definition
│   ├── 📄 linkedin_automation.py    # Selenium automation script
│   └── 📄 linkedin_automation_playwright.py # Playwright automation script
├── 📁 webui/                        # Web user interface
│   ├── 📁 backend/                  # Backend API service
│   │   ├── 📄 main.py              # FastAPI main application
│   │   └── 📄 linkedin_api.py      # LinkedIn API route handlers
│   └── 📁 frontend/                 # React frontend application
│       ├── 📁 src/                 # Frontend source code
│       │   ├── 📄 App.jsx          # Main application component
│       │   ├── 📄 LinkedInAutomation.jsx  # LinkedIn automation interface
│       │   ├── 📄 LinkedInContext.jsx     # State management context
│       │   ├── 📄 ErrorBoundary.jsx       # Error boundary component
│       │   └── 📄 main.jsx         # Application entry point
│       ├── 📄 package.json         # Frontend dependencies
│       ├── 📄 vite.config.js       # Vite build configuration
│       └── 📄 index.html           # HTML template
├── 📁 data_folder/                  # Data folder
│   ├── 📄 plain_text_resume.yaml   # Resume data template
│   ├── 📄 secrets.yaml             # API keys configuration
│   └── 📄 work_preferences.yaml    # Work preference settings
├── 📄 config.py                     # Application configuration
├── 📄 main.py                       # Main program entry
├── 📄 requirements.txt              # Python core dependencies
├── 📄 requirements_linkedin.txt     # LinkedIn-specific dependencies
└── 📄 README.md                     # Project documentation
```

## 🤝 Contributing

We welcome contributions to improve AIHawk! Please see our [Contributing Guidelines](CONTRIBUTING.md) for detailed information.

### Quick Contribution Guide

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/AmazingFeature`)
3. **Commit** your changes (`git commit -m 'Add some AmazingFeature'`)
4. **Push** to the branch (`git push origin feature/AmazingFeature`)
5. **Open** a Pull Request

### Development Guidelines

- Follow PEP 8 standards for Python code
- Include docstrings for new functions and classes
- Add tests for new functionality
- Update documentation as needed

### Bug Reports and Feature Requests

- Use [GitHub Issues](https://github.com/UJJacky/Jobs-Application_Linkedin_AIHawk/issues) to report bugs
- Provide detailed error information and reproduction steps
- Include system environment and version information

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

Thanks to the following open source projects and contributors:

- [Google Gemini](https://ai.google.dev/) - Powerful AI language model
- [FastAPI](https://fastapi.tiangolo.com/) - Modern web framework
- [React](https://reactjs.org/) - Excellent frontend framework
- [Material-UI](https://mui.com/) - Professional UI component library
- [Playwright](https://playwright.dev/) - Modern browser automation tool
- [Selenium](https://selenium.dev/) - Reliable web automation framework

---

<div align="center">

**Made with ❤️ for job seekers worldwide**

*Star ⭐ this repository if it helped you land your dream job!*

</div>


# 🎯 Agent Jobotin | 智能求职助手 | LinkedIn 求职助手 
# 简历AI优化生成 # 个性化求职信生成 # 职位申请自动化

![AIHawk Header](assets/readme_header.png)

## 🎯 项目概述

Agent Jobotin 是一个基于Github上_AIHawk（Auto_Jobs_Applier_AIHawk）开源项目代码，以及借鉴了_Auto_job_applier_linkedIn（GodsScion）技术栈：Python + Selenium + Undetected Chromedriver，以Google Gemini 2.5 作为LLM驱动的智能求职助手平台，专为现代求职者设计。通过 AI 驱动的智能简历优化、智能个性化求职信生成和 LinkedIn 职位特别是Easy Apply 的自动化申请，帮助您高效地找到理想工作。
### 🌟 核心价值
- 🤖 AI 智能化: 基于 Google Gemini 2.5 Flash Preview LLM模型API
- ⚡ 高效自动化: 浏览器自动可操作化，LinkedIn 智能化申请速度优化至 180-240 秒/份
- 🎯 精准匹配: 智能分析职位要求，定制化简历内容，AI优选关键词职位搜索结果
- 🌐 现代化界面: React Vite + FastAPI 构建的现代 Web 应用
- 🔒 安全可靠: 支持 2FA 验证，密钥管理，登录自动化设置 数据本地存储
- 🛠️ 智能工具: 智能生成求职信，根据JD智能优化简历，智能匹配职位描述，智能搜索职位列表，批量申请，智能跟踪职位邮件通知等（待二次开发）

🚀 主要功能模块

📄 智能简历优化 
- AI驱动定制化：基于Google Gemini 2.5 Flash Preview，专业招聘提示词库+智能分析职位要求并优化简历内容
- PDF上传支持：上传现有简历进行AI分析和针对性优化
- 专业模板：多种优雅简约ATS高适配的简历模板适用于不同情境，Skills可视化词条
- 实时预览：HTML当前窗口直接预览，并可下载及配合高质量优雅PDF导出
- 多语言支持：完美支持中英文简历
- 内容高亮：优化内容用✨图标highlight标记，可选择隐藏高亮审查具体优化点

💌 智能求职信生成 
- 个性化内容：基于公司信息和职位要求生成500+字定制化求职信
- 公司Logo集成：自动获取目标公司Logo提升专业度
- 双语支持：支持中英文求职信生成
- 匹配度分析：可视化图表展示候选人-职位匹配度
- 优雅模板：专业设计的求职信模板，支持HTML/PDF导出

🤖 LinkedIn自动化申请 
- 智能职位搜索：基于关键词、地点等条件精准搜索，易于管理筛选浏览的职位搜索结果列表
- 批量Easy Apply申请：支持大规模自动化申请
- 多种自动化模式：Selenium + Undetected Chrome（推荐 稳定可靠）、Playwright 同步/异步 （可选）
- 智能问答处理：自动处理LinkedIn申请过程中的各种问题类型
- 申请状态跟踪：实时显示"申请"→"申请中"→"已申请"状态
- 职位发布时间：提取并显示职位发布时间，支持时间排序
- 成功通知自动关闭：自动检测并关闭申请成功通知窗口
- Chrome清理功能：安全清理自动化数据，解决连接问题
- 智能过滤防重复：避免重复申请，详细申请历史记录
- 2FA验证支持：完整支持LinkedIn两步验证
- 支持浏览器反爬虫检测优化

🌐 现代化WebUI界面 
- 响应式设计：支持桌面和移动设备
- 实时日志显示：WebSocket实时日志流，反馈详细的职位搜索和申请进度
- 会话数据持久化：浏览器会话内数据保持
- 优雅的UI组件：Material-UI组件库现代化网站布局设计
- 直观用户界面：流畅体验和实时更新

🛠️ 技术架构

核心技术栈
- 前端：React 18 + Vite + Material-UI
- 后端：Python 3.8+ + FastAPI
- AI引擎：Google Gemini 2.5 Flash Preview (速度能力高性价比推荐)
- 自动化：Selenium + Undetected Chrome (推荐稳定)
- 数据存储：YAML配置文件+预置Linkedin问题回答等

性能优化特性
- ⚡ 申请速度提升：优化后平均180-240秒/申请 (减少5-10秒)
- 🎯 智能等待策略：使用WebDriverWait替代固定延迟
- 🔄 三阶段滚动及优化抓取：确保完整职位列表加载
- 🚀 Gemini 2.5 Flash：2-3x faster than GPT models
- 💰 成本效率：80% lower cost than OpenAI

🎯 项目状态

当前状态: 🎉 **测试98%通过率运行状态**
- ✅ LinkedIn自动化系统完全稳定 职位搜索300″/50 jobs
- ✅ AI简历优化功能完善
- ✅ 求职信生成系统完整
- ✅ Web界面响应流畅
- ✅ 二次开发TBD

## 📋 安装指南

### 🔧 环境配置要求

系统环境
- Python: 3.10+ （推荐使用 3.11 版本）
- Node.js: 18+ 和 npm 包管理器
- Git: 用于代码版本控制
- Chrome 浏览器: 最新版本（用于自动化）/Undetected ChromeDriver (反检测优化)
- 操作系统: Windows 10/11, macOS 10.15+, 或 Ubuntu 18.04+

硬件要求
- 内存: 最低 4GB RAM，推荐 8GB+
- 存储: 至少 2GB 可用空间
- 网络: 稳定的互联网连接 （包含科学上网）

### 🚀 详细安装步骤

第一步：Python 环境设置
```bash
# 创建虚拟环境（推荐）
python -m venv aihawk_env

# 激活虚拟环境
# Windows:
aihawk_env\Scripts\activate
# macOS/Linux:
source aihawk_env/bin/activate

# 升级 pip
python -m pip install --upgrade pip
```

第二步：安装 Python 依赖
```bash
# 安装核心依赖包
pip install -r requirements.txt

# 可选：安装LinkedIn特定依赖
pip install -r requirements_linkedin.txt

# 验证关键包安装
python -c "import fastapi, selenium, playwright; print('✅ Python 依赖安装成功！')"
```

第三步：前端环境配置
```bash
# 进入前端目录
cd webui/frontend

# 安装 Node.js 依赖
npm install

# 验证安装
npm list --depth=0

# 返回项目根目录
cd ../..
```

### 🎭 浏览器自动化配置

Selenium配置（推荐稳定）
```bash
# ChromeDriver 由 webdriver-manager 自动管理
# 无需额外配置，首次运行时自动下载
```

Playwright 配置（备选）
```bash
# 安装 Playwright 浏览器
playwright install chromium

# 安装系统依赖（Linux）
playwright install-deps
```

### ⚙️ API 密钥配置

 配置文件设置
```bash
# 复制配置模板
cp data_folder/secrets.yaml.example data_folder/secrets.yaml

# 编辑配置文件
# 必需：gemini_api_key
# 可选：openai_api_key, linkedin_email, linkedin_password
```

secrets.yaml 配置示例
```yaml
# Gemini API (推荐 - 性能更好，成本更低)
gemini_api_key: "your-gemini-api-key-here"

# OpenAI API (备选)
openai_api_key: "your-openai-api-key-here"

# LinkedIn账户 (可选，也可在界面中输入)
linkedin_email: "<EMAIL>"
linkedin_password: "your-password"
```

获取 API 密钥
1. Google Gemini API:
   - 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
   - 创建新的 API 密钥
   - 复制密钥到 `secrets.yaml` 文件

2. OpenAI API（可选）:
   - 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
   - 创建新的 API 密钥

### 🔍 安装验证
```bash
# 验证完整安装
python main.py --check

# 启动应用测试
python main.py

# 检查服务状态
curl http://localhost:8000/health
```

### 🐳 Docker 部署（可选）

```bash
# 使用 Docker Compose 一键部署
docker-compose up --build -d

# 查看服务状态
docker-compose ps

# 访问应用：http://localhost:8000
```

## 📖 使用指南

🎯 第一次使用指南

1. 访问Web界面
打开浏览器访问 http://localhost:3000

2. 配置API密钥
- 进入设置页面
- 配置Gemini API密钥 (推荐，性能更好成本更低)
- 或配置OpenAI API密钥作为备选

3. LinkedIn自动化设置
- 进入"LinkedIn自动化"页面
- 点击"设置自动化"按钮 (选择Selenium模式，最稳定)
- 在弹出的Chrome浏览器中手动登录LinkedIn
- 点击"验证登录状态"确认登录成功

4. 开始职位搜索和申请
- 设置搜索关键词和地点
- 点击"搜索职位"
- 查看搜索结果，包含职位发布时间
- 选择职位进行单个或批量申请
- 实时监控申请进度和状态


### 🎯 核心功能使用

🎨 智能简历优化
1. 上传现有简历PDF或从头创建
2. 输入目标职位URL进行AI分析
3. AI自动优化内容，用✨图标标记优化部分
4. 实时预览效果，支持隐藏/显示高亮
5. 导出高质量PDF

💌 求职信生成
1. 输入目标公司和职位信息
2. AI生成500+字个性化求职信
3. 自动获取公司Logo提升专业度
4. 查看匹配度分析图表
5. 下载HTML/PDF格式

🤖 LinkedIn自动化
1. **设置自动化**：选择自动化类型（Playwright/Selenium推荐）
2. **登录验证**：输入LinkedIn凭据（支持2FA）
3. **搜索配置**：设置职位搜索条件（关键词、地点、筛选器）
4. **申请设置**：配置申请偏好和自动回答
5. **开始自动化**：启动自动化职位搜索和申请流程
6. **监控进度**：通过详细日志实时跟踪申请

### ⚡ 最新优化功能

LinkedIn申请流程优化
- **申请速度提升**：优化后平均90-120秒/申请 (减少5-10秒)
- **智能等待策略**：使用WebDriverWait替代固定延迟
- **自动成功通知关闭**：无需手动关闭申请成功弹窗
- **职位发布时间显示**：支持按时间排序筛选

Chrome清理功能
- **一键清理**：运行状态卡片中的"🧹 清理Chrome数据"按钮
- **安全清理**：只清理自动化数据，不影响个人Chrome
- **解决连接问题**：修复Undetected Chrome连接失败

UI/UX改进
- **实时日志显示**：详细的申请进度反馈
- **按钮状态优化**：文本不换行，状态清晰
- **响应式布局**：完美支持桌面和移动设备

### 💡 使用建议

最佳实践
1. **首次使用先测试**：建议先申请5-10个职位测试效果
2. **定期清理Chrome**：每周执行一次Chrome数据清理
3. **监控申请成功率**：根据反馈调整搜索条件
4. **合理设置申请频率**：避免过于频繁申请

注意事项
- **网络稳定性**：确保稳定的网络连接
- **LinkedIn账户活跃**：定期手动使用LinkedIn保持活跃
- **API配额管理**：合理使用Gemini API配额
- **Chrome版本**：保持Chrome浏览器更新

### ⚙️ 设置管理

访问设置面板配置：
- **自动化偏好**: 浏览器设置、超时、重试次数
- **申请限制**: 每日申请限制和间隔
- **数据管理**: 备份、导出和导入设置
- **UI偏好**: 主题、语言和通知设置

## ⚙️ 配置说明

### 基础配置

AI模型配置 (config.py)
```python
# AI模型设置
LLM_MODEL = 'gemini-2.5-flash-preview-0514'  # 推荐
LLM_TEMPERATURE = 1.0
LLM_TIMEOUT = 60
LLM_MAX_RETRIES = 3
```

LinkedIn自动化配置 (linkedin_config.yaml)
```yaml
selenium:
  headless: false  # 推荐关闭，便于处理验证
  timeout: 30
  window_size: [1200, 800]

automation:
  max_applications_per_day: 50
  application_delay: [30, 60]  # 秒
```

工作偏好设置 (data_folder/work_preferences.yaml)
```yaml
# Job Search Preferences
remote_work: true
willing_to_relocate: false
salary_range: [50000, 100000]
experience_level: "mid"

# Application Settings
max_applications_per_day: 50
application_delay: [30, 60]  # seconds
```

### 高级配置

浏览器自动化设置
```yaml
# Selenium配置
selenium:
  headless: false
  timeout: 30
  window_size: [1200, 800]

# Playwright配置
playwright:
  headless: false
  timeout: 30000
  browser_type: "chromium"
```

### 环境特定设置

开发环境
```bash
# 启用调试模式
export DEBUG=true
export LOG_LEVEL=DEBUG

# 使用开发端口
export FRONTEND_PORT=3000
export BACKEND_PORT=8003
```

生产环境
```bash
# 生产设置
export NODE_ENV=production
export LOG_LEVEL=INFO

# 安全设置
export SECURE_COOKIES=true
export HTTPS_ONLY=true
```

## 🔧 故障排除

常见安装问题

Python依赖问题
```bash
# 升级pip和setuptools
pip install --upgrade pip setuptools wheel

# 强制重新安装
pip install --force-reinstall package-name
```

Node.js依赖问题
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 浏览器自动化问题

Chrome自动化问题
```bash
# 检查Chrome版本
google-chrome --version

# 更新webdriver-manager
pip install --upgrade webdriver-manager
```

Playwright问题
```bash
# 重新安装浏览器
python -m playwright uninstall
python -m playwright install chromium

# 检查浏览器安装
python -m playwright install --help
```

### LinkedIn自动化问题

登录问题
- **2FA验证**: 关闭无头模式手动完成2FA
- **验证码**: 使用GUI模式手动完成验证码
- **频率限制**: 减少申请频率并增加延迟

申请失败
- **Undetected Chrome连接失败**：使用Chrome清理功能
- **申请按钮无响应**：检查登录状态，重新验证登录
- **职位加载不完整**：系统已优化三阶段滚动策略
- **申请速度慢**：系统已优化，现在平均90-120秒/申请

### 常见问题解答

网络和连接问题
- **Gemini API**: 检查API密钥有效性和配额
- **OpenAI API**: 验证API密钥和计费状态
- **频率限制**: 实施指数退避策略

#### 性能问题
```bash
# 监控内存使用
htop  # Linux/Mac
taskmgr  # Windows

# 减少浏览器实例
# 在配置中设置 headless: true
```

### 获取帮助

日志文件
- 后端日志: 控制台输出或 `log/` 目录中的日志文件
- 前端日志: 浏览器开发者工具控制台
- 自动化日志: `log/selenium.log` 或 `log/playwright.log`

调试模式
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
export DEBUG=true

# 运行调试输出
python main.py --debug
```

社区支持
- **GitHub Issues**: 报告错误和请求功能
- **讨论区**: 提问和分享经验
- **文档**: 查看此README和内联代码注释

### 导入修复总结

#### 问题描述
本次修复解决了Pyright在项目中检测到的多个导入错误，这些错误主要由于模块路径不正确或缺少必要的导入语句导致。

#### 修复内容
通过改进导入逻辑和添加缺失的导入语句，修复了以下文件中的导入问题：

**修复的文件列表：**
1. `src/libs/resume_and_cover_builder/llm/llm_job_parser.py`
2. `src/libs/resume_and_cover_builder/llm/llm_generate_resume.py`
3. `src/libs/resume_and_cover_builder/llm/llm_generate_tailored_resume.py`
4. `src/libs/resume_and_cover_builder/resume_facade.py`
5. `src/libs/resume_and_cover_builder/style_manager.py`
6. `src/libs/resume_and_cover_builder/resume_generator.py`
7. `src/libs/llm_manager.py`
8. `src/linkedin_automation.py`
9. `src/linkedin_automation_playwright.py`
10. `webui/backend/main.py`
11. `webui/backend/linkedin_api.py`
12. `main.py`

#### 验证结果

**导入验证：** ✅ 所有导入语句现在都能正确解析
**方法存在性验证：** ✅ 所有引用的方法和属性都存在于相应的类中
**功能验证：** ✅ 核心功能（简历生成、LinkedIn自动化）运行正常
**真实LinkedIn测试：** ✅ LinkedIn自动化功能在真实环境中测试通过

#### LinkedIn加载模式分析

**观察到的LinkedIn加载行为：**
- **初始加载**：页面首先显示基本结构
- **动态内容加载**：职位列表通过AJAX异步加载
- **滚动触发加载**：滚动到底部时触发更多内容加载
- **反爬虫机制**：LinkedIn具有检测自动化行为的机制

**应对策略：**
- 使用智能等待策略而非固定延迟
- 模拟人类滚动行为
- 合理设置请求间隔
- 使用Undetected Chrome避免检测

#### 技术实现亮点

1. **模块化设计**：每个功能模块都有清晰的职责分工
2. **错误处理**：完善的异常处理机制确保程序稳定性
3. **配置管理**：灵活的配置系统支持不同环境的部署
4. **日志系统**：详细的日志记录便于问题诊断
5. **API集成**：支持多种AI模型（Gemini、OpenAI）的无缝切换

#### 使用建议

**开发环境：**
- 确保Python 3.10+环境
- 安装所有必需的依赖包
- 配置正确的API密钥

**生产环境：**
- 使用虚拟环境隔离依赖
- 定期更新Chrome浏览器
- 监控API使用配额
- 合理设置自动化频率以避免被LinkedIn限制


### 🔧 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (React)                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  LinkedIn Auto  │  │  Resume Gen     │  │  Cover Letter│ │
│  │  Interface      │  │  Interface      │  │  Interface   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│                           │                                 │
│                    WebSocket + HTTP API                     │
└─────────────────────────────┼───────────────────────────────┘
                              │
┌─────────────────────────────┼───────────────────────────────┐
│                    Backend (FastAPI)                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  LinkedIn API   │  │  Resume API     │  │  Settings API│ │
│  │  Endpoints      │  │  Endpoints      │  │  Endpoints   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│                           │                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Automation     │  │  AI Engine      │  │  Utils &     │ │
│  │  Engine         │  │  (Gemini/GPT)   │  │  Helpers     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────┼───────────────────────────────┘
                              │
┌─────────────────────────────┼───────────────────────────────┐
│                    External Services                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  LinkedIn       │  │  Google Gemini  │  │  Chrome      │ │
│  │  Platform       │  │  API            │  │  Browser     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘

```

## 📁 项目结构

Jobs-Application_Linkedin_AIHawk/
├── 📁 src/                          # 核心源代码
│   ├── 📁 libs/                     # 核心库
│   │   ├── 📁 resume_and_cover_builder/  # 简历和求职信生成
│   │   │   ├── 📁 llm/              # LLM相关模块
│   │   │   │   ├── 📄 llm_job_parser.py      # 职位信息解析
│   │   │   │   ├── 📄 llm_generate_resume.py # 简历内容生成
│   │   │   │   └── 📄 llm_generate_tailored_resume.py # 定制简历优化
│   │   │   ├── 📁 resume_style/     # 简历样式模板
│   │   │   │   ├── 📄 style_josylad_blue.css    # 蓝色主题模板
│   │   │   │   ├── 📄 style_josylad_grey.css    # 灰色主题模板
│   │   │   │   └── 📄 style_krishnavalliappan.css # 专业模板
│   │   │   ├── 📄 resume_facade.py  # 简历生成门面
│   │   │   ├── 📄 style_manager.py  # 样式管理器
│   │   │   └── 📄 resume_generator.py # 简历生成器
│   │   └── 📄 llm_manager.py        # LLM统一管理器
│   ├── 📁 utils/                    # 工具模块
│   │   ├── 📄 chrome_utils.py       # Chrome浏览器工具
│   │   └── 📄 constants.py          # 常量定义
│   ├── 📄 linkedin_automation.py    # Selenium自动化脚本
│   └── 📄 linkedin_automation_playwright.py # Playwright自动化脚本
├── 📁 webui/                        # Web用户界面
│   ├── 📁 backend/                  # 后端API服务
│   │   ├── 📄 main.py              # FastAPI主应用
│   │   └── 📄 linkedin_api.py      # LinkedIn API路由处理器
│   └── 📁 frontend/                 # React前端应用
│       ├── 📁 src/                 # 前端源代码
│       │   ├── 📄 App.jsx          # 主应用组件
│       │   ├── 📄 LinkedInAutomation.jsx  # LinkedIn自动化界面
│       │   ├── 📄 LinkedInContext.jsx     # 状态管理上下文
│       │   ├── 📄 ErrorBoundary.jsx       # 错误边界组件
│       │   └── 📄 main.jsx         # 应用入口点
│       ├── 📄 package.json         # 前端依赖
│       ├── 📄 vite.config.js       # Vite构建配置
│       └── 📄 index.html           # HTML模板
├── 📁 data_folder/                  # 数据文件夹
│   ├── 📄 plain_text_resume.yaml   # 简历数据模板
│   ├── 📄 secrets.yaml             # API密钥配置
│   └── 📄 work_preferences.yaml    # 工作偏好设置
├── 📄 config.py                     # 应用配置
├── 📄 main.py                       # 主程序入口
├── 📄 requirements.txt              # Python核心依赖
├── 📄 requirements_linkedin.txt     # LinkedIn特定依赖
└── 📄 README.md                     # 项目文档


## 🤝 贡献指南

我们欢迎对AIHawk的改进贡献！请查看我们的[贡献指南](CONTRIBUTING.md)获取详细信息。

### 快速贡献指南

1. **Fork** 仓库
2. **创建** 功能分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 更改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **打开** Pull Request

### 开发指南

- 遵循Python代码的PEP 8标准
- 为新函数和类包含文档字符串
- 为新功能添加测试
- 根据需要更新文档

### 错误报告和功能请求

- 使用 [GitHub Issues](https://github.com/UJJacky/Jobs-Application_Linkedin_AIHawk/issues) 报告错误
- 提供详细的错误信息和重现步骤
- 包含系统环境和版本信息

## 📄 许可证

本项目采用MIT许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢以下开源项目和贡献者：

- [Google Gemini](https://ai.google.dev/) - 强大的AI语言模型
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Web框架
- [React](https://reactjs.org/) - 优秀的前端框架
- [Material-UI](https://mui.com/) - 专业UI组件库
- [Playwright](https://playwright.dev/) - 现代浏览器自动化工具
- [Selenium](https://selenium.dev/) - 可靠的Web自动化框架

---


**Made with ❤️ for job seekers worldwide**

*如果这个项目帮助您找到了理想工作，请给我们一个 ⭐ Star！*

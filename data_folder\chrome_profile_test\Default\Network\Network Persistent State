{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABQAAABodHRwczovL2xpbmtlZGluLmNvbQ==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "www.linkedin.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2xpbmtlZGluLmNvbQ==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL2xpbmtlZGluLmNvbQ==", false, 0], "server": "https://dpm.demdex.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2xpbmtlZGluLmNvbQ==", true, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2xpbmtlZGluLmNvbQ==", false, 0], "server": "https://www.linkedin.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "3G"}}}
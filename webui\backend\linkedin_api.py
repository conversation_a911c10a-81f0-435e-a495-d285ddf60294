"""LinkedIn自动化API端点"""

from typing import List, Dict, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field
from loguru import logger
import sys
import os
import json
import glob
from pathlib import Path # 确保导入Path
import time
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

# 添加项目根目录到Python路径
# __file__ 指向 d:\Jobs_Applier_AI_Agent_AIHawk-main\webui\backend\linkedin_api.py
# .resolve().parents[2] 会得到项目根目录 d:\Jobs_Applier_AI_Agent_AIHawk-main
project_root_dir = Path(__file__).resolve().parents[2]

if str(project_root_dir) not in sys.path:
    sys.path.insert(0, str(project_root_dir))

try:
    from src.linkedin_automation import LinkedInAutomation as SeleniumAutomation
    logger.info("Selenium自动化类导入成功")
except ImportError as e:
    logger.warning(f"Selenium自动化类导入失败: {e}")
    SeleniumAutomation = None

try:
    from src.linkedin_automation_playwright import LinkedInAutomationPlaywright as PlaywrightAutomation
    logger.info("Playwright同步自动化类导入成功")
except ImportError as e:
    logger.warning(f"Playwright同步自动化类导入失败: {e}")
    PlaywrightAutomation = None

try:
    from src.linkedin_automation_playwright_async import LinkedInAutomationPlaywrightAsync as PlaywrightAsyncAutomation
    logger.info("Playwright异步自动化类导入成功")
except ImportError as e:
    logger.warning(f"Playwright异 asynchronous 自动化类导入失败: {e}")
    PlaywrightAsyncAutomation = None

# 根据配置文件选择默认自动化工具
try:
    import yaml
    config_path = Path(__file__).resolve().parents[2] / "linkedin_config.yaml"
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            automation_tool = config.get('automation_tool', 'selenium')
            if automation_tool == 'playwright' and PlaywrightAutomation:
                LinkedInAutomation = PlaywrightAutomation
                logger.info("根据配置文件使用 Playwright 自动化工具")
            elif automation_tool == 'playwright_async' and PlaywrightAsyncAutomation:
                LinkedInAutomation = PlaywrightAsyncAutomation
                logger.info("根据配置文件使用 Playwright 异步自动化工具")
            else:
                LinkedInAutomation = SeleniumAutomation
                logger.info("根据配置文件使用 Selenium 自动化工具")
    else:
        LinkedInAutomation = SeleniumAutomation  # 默认Selenium
        logger.info("配置文件不存在，使用默认 Selenium 自动化工具")
except Exception as e:
    logger.warning(f"读取配置文件失败，使用默认 Selenium: {e}")
    LinkedInAutomation = SeleniumAutomation

router = APIRouter(prefix="/api/linkedin", tags=["LinkedIn自动化"])

# WebSocket日志推送相关
log_clients = set()

@router.websocket("/ws/logs")
async def websocket_logs(websocket: WebSocket):
    await websocket.accept()
    log_clients.add(websocket)

    # 发送连接成功消息
    try:
        welcome_message = {
            "message": "🎉 实时日志连接已建立，准备接收搜索日志...",
            "type": "info",
            "timestamp": time.time()
        }
        await websocket.send_text(json.dumps(welcome_message, ensure_ascii=False))
    except Exception as e:
        print(f"发送欢迎消息失败: {e}")

    try:
        while True:
            await asyncio.sleep(1)  # 保持连接
    except WebSocketDisconnect:
        log_clients.remove(websocket)

async def push_log(message: str, log_type: str = "info"):
    """推送日志消息到所有WebSocket客户端

    Args:
        message: 日志消息
        log_type: 日志类型 (info, success, error, start, complete)
    """
    log_data = {
        "message": message,
        "type": log_type,
        "timestamp": time.time()
    }

    for ws in list(log_clients):
        try:
            await ws.send_text(json.dumps(log_data, ensure_ascii=False))
        except Exception:
            log_clients.remove(ws)

async def push_search_lifecycle_event(event_type: str, data: dict = None):
    """推送搜索生命周期事件

    Args:
        event_type: 事件类型 (search_start, search_complete, search_error)
        data: 附加数据
    """
    event_data = {
        "event": event_type,
        "data": data or {},
        "timestamp": time.time()
    }

    for ws in list(log_clients):
        try:
            await ws.send_text(json.dumps(event_data, ensure_ascii=False))
        except Exception:
            log_clients.remove(ws)

# 配置日志记录器
log_file_path = Path(__file__).parent / "log" / "linkedin_automation.log"
log_file_path.parent.mkdir(parents=True, exist_ok=True)

# 简化的日志处理 - 直接在API调用中推送实时日志

def clear_job_html_files():
    """清空webui/backend目录中的职位HTML文件"""
    try:
        backend_dir = Path(__file__).parent

        # 定义要清理的HTML文件模式
        html_patterns = [
            "job_cards_preview_page_*.html",  # Selenium生成的分页HTML文件
            "linkedin_jobs_*.html",           # Playwright生成的HTML快照
            "linkedin_jobs_playwright.html",  # Playwright同步版本
            "linkedin_jobs_playwright_async.html",  # Playwright异步版本
        ]

        cleared_files = []
        for pattern in html_patterns:
            files = glob.glob(str(backend_dir / pattern))
            for file_path in files:
                try:
                    os.remove(file_path)
                    cleared_files.append(os.path.basename(file_path))
                    logger.debug(f"已删除HTML文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除HTML文件失败 {file_path}: {e}")

        # 同时清理log目录中的HTML文件
        log_dir = backend_dir.parent / "log"
        if log_dir.exists():
            log_html_patterns = [
                "linkedin_jobs_*.html",
                "job_cards_*.html"
            ]
            for pattern in log_html_patterns:
                files = glob.glob(str(log_dir / pattern))
                for file_path in files:
                    try:
                        os.remove(file_path)
                        cleared_files.append(f"log/{os.path.basename(file_path)}")
                        logger.debug(f"已删除log目录HTML文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"删除log目录HTML文件失败 {file_path}: {e}")

        if cleared_files:
            logger.info(f"🧹 已清理 {len(cleared_files)} 个旧的职位HTML文件: {', '.join(cleared_files)}")
        else:
            logger.debug("🧹 没有找到需要清理的HTML文件")

    except Exception as e:
        logger.error(f"清理HTML文件时出错: {e}")

# 配置loguru记录器，同时输出到控制台和文件
logger.remove()  # 移除默认处理器
logger.add(sys.stderr, level="INFO")  # 添加标准错误输出处理器
logger.add(
    str(log_file_path),
    rotation="10 MB",  # 日志文件大小达到10MB时轮转
    retention="1 week",  # 保留1周的日志
    compression="zip",  # 压缩旧日志
    level="DEBUG",  # 记录DEBUG及以上级别的日志
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
)

logger.info(f"日志文件路径: {log_file_path}")

# 全局LinkedIn自动化实例
linkedin_automation = None
automation_status = {
    "is_running": False,
    "is_logged_in": False,
    "current_task": None,
    "automation_type": "selenium",  # 默认使用selenium，可选"playwright", "playwright_async"
    "progress": {
        "total_found": 0,
        "total_applied": 0,
        "successful_applications": [],
        "failed_applications": []
    }
}

# 线程池执行器，用于在同步环境中运行 Playwright
executor = ThreadPoolExecutor(max_workers=1)

def run_in_thread(func, *args, **kwargs):
    """在线程池中运行同步函数"""
    return func(*args, **kwargs)

# Pydantic模型
class LoginRequest(BaseModel):
    email: str = Field(..., description="LinkedIn邮箱")
    password: str = Field(..., description="LinkedIn密码")
    headless: bool = Field(default=False, description="是否无头模式")

class SearchRequest(BaseModel):
    keywords: str = Field(..., description="搜索关键词")
    location: str = Field(default="United States", description="搜索地点")
    easy_apply_only: bool = Field(default=True, description="仅Easy Apply职位")

class BatchApplyRequest(BaseModel):
    max_applications: int = Field(default=20, description="最大申请数量")
    keywords: List[str] = Field(default=["python developer"], description="搜索关键词列表")
    location: str = Field(default="United States", description="搜索地点")

class JobInfo(BaseModel):
    title: str
    company: str
    location: str
    url: str
    is_easy_apply: bool
    job_id: str
    posted_time: Optional[str] = None  # 🔧 新增：职位发布时间

class SetupRequest(BaseModel):
    headless: bool = Field(default=False, description="是否无头模式")
    automation_type: str = Field(default="playwright_async", description="自动化类型: selenium, playwright, playwright_async")

class AutomationStatus(BaseModel):
    is_running: bool
    is_logged_in: bool
    current_task: Optional[str]
    progress: Dict

# API端点
@router.get("/status", response_model=AutomationStatus)
async def get_automation_status():
    """获取自动化状态"""
    return AutomationStatus(**automation_status)

@router.post("/setup")
async def setup_automation(request: SetupRequest):
    """设置LinkedIn自动化，支持Selenium/Playwright/PlaywrightAsync切换"""
    global linkedin_automation, LinkedInAutomation, automation_status

    automation_type = request.automation_type
    headless = request.headless

    if automation_type == "playwright_async":
        LinkedInAutomation = PlaywrightAsyncAutomation
        automation_status["automation_type"] = "playwright_async"
    elif automation_type == "playwright":
        LinkedInAutomation = PlaywrightAutomation
        automation_status["automation_type"] = "playwright"
    else:
        LinkedInAutomation = SeleniumAutomation
        automation_status["automation_type"] = "selenium"

    if LinkedInAutomation is None:
        raise HTTPException(status_code=500, detail="LinkedIn自动化模块未正确导入，请检查依赖安装")

    try:
        logger.info(f"开始设置 {automation_type} 自动化...")

        if linkedin_automation:
            logger.info("关闭现有自动化实例...")
            old_automation = linkedin_automation
            linkedin_automation = None  # 先清空全局变量

            try:
                if automation_status.get("automation_type") == "playwright_async":
                    # 直接调用异步 close 方法
                    if hasattr(old_automation, 'close') and old_automation.close:
                        await old_automation.close()
                elif automation_status.get("automation_type") == "playwright":
                    # 在线程池中运行 Playwright 的 close 方法
                    if hasattr(old_automation, 'close') and old_automation.close:
                        await asyncio.get_event_loop().run_in_executor(executor, old_automation.close)
                else:
                    # Selenium
                    if hasattr(old_automation, 'close') and old_automation.close:
                        old_automation.close()
                logger.info("现有自动化实例已关闭")
            except Exception as close_error:
                logger.warning(f"关闭现有自动化实例时出错: {close_error}")
                # 继续执行，不要因为关闭失败而中断设置过程

        config_path = os.path.join(
            os.path.dirname(__file__), '..', '..', 'linkedin_config.yaml'
        )
        logger.info(f"配置文件路径: {config_path}")
        logger.info(f"创建 {automation_type} 自动化实例...")

        # 根据自动化类型创建不同的实例
        if automation_type == "playwright_async":
            linkedin_automation = PlaywrightAsyncAutomation(config_path)
        elif automation_type == "playwright":
            linkedin_automation = PlaywrightAutomation(config_path)
        else:  # selenium
            linkedin_automation = SeleniumAutomation(config_path)

        logger.info(f"自动化实例创建成功: {type(linkedin_automation)}")

        if automation_type == "playwright_async":
            logger.info("设置异步 Playwright 浏览器...")
            logger.info(f"当前事件循环: {asyncio.get_event_loop()}")
            logger.info(f"是否在主线程: {threading.current_thread() == threading.main_thread()}")

            # 尝试关闭可能存在的 Selenium WebDriver 以避免冲突
            try:
                from webui.backend.main import resume_facade
                if hasattr(resume_facade, 'driver') and resume_facade.driver:
                    logger.info("检测到 Selenium WebDriver，尝试关闭以避免冲突...")
                    resume_facade.driver.quit()
                    resume_facade.driver = None
                    logger.info("Selenium WebDriver 已关闭")
            except Exception as selenium_close_error:
                logger.warning(f"关闭 Selenium WebDriver 时出错: {selenium_close_error}")

            # 尝试设置浏览器
            try:
                # 直接调用异步方法
                await linkedin_automation.setup_driver(headless=headless)
                logger.info("异步 Playwright 浏览器设置完成")
            except Exception as setup_error:
                logger.error(f"浏览器设置失败，详细错误: {setup_error}")
                logger.error(f"错误类型: {type(setup_error)}")

                # 尝试重新创建实例
                logger.info("尝试重新创建自动化实例...")
                try:
                    await linkedin_automation.close()
                    linkedin_automation = PlaywrightAsyncAutomation(config_path)
                    await linkedin_automation.setup_driver(headless=headless)
                    logger.info("重新创建成功")
                except Exception as retry_error:
                    logger.error(f"重新创建也失败: {retry_error}")
                    raise setup_error
        elif automation_type == "playwright":
            logger.info("设置同步 Playwright 浏览器...")
            # 在新线程中运行 Playwright 的 setup_driver 方法，避免 asyncio 循环冲突
            def setup_playwright():
                logger.info(f"在线程中设置 Playwright，headless={headless}")
                try:
                    # 在新线程中，没有 asyncio 循环，可以安全使用同步 Playwright
                    result = linkedin_automation.setup_driver(headless=headless)
                    logger.info(f"Playwright 设置完成，返回: {type(result)}")
                    return result
                except Exception as e:
                    logger.error(f"线程中设置 Playwright 失败: {e}")
                    raise e

            # 使用 ThreadPoolExecutor 在新线程中运行
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                future = thread_executor.submit(setup_playwright)
                # 等待线程完成
                result = future.result(timeout=60)  # 60秒超时
            logger.info("同步 Playwright 浏览器设置完成")
        else:
            logger.info("设置 Selenium 浏览器...")
            linkedin_automation.setup_driver(headless=headless)
            logger.info("Selenium 浏览器设置完成")

        automation_status["current_task"] = "已设置浏览器"
        logger.info(f"{automation_type} 自动化设置成功")
        return {"success": True, "message": f"LinkedIn自动化设置成功，当前类型: {automation_type}"}
    except Exception as e:
        logger.error(f"设置LinkedIn自动化失败: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"设置失败: {str(e)}")

@router.post("/login")
async def login_linkedin(request: LoginRequest):
    """登录LinkedIn"""
    global linkedin_automation, automation_status

    if not linkedin_automation:
        raise HTTPException(status_code=400, detail="请先设置自动化")

    try:
        automation_status["current_task"] = "正在登录LinkedIn..."
        automation_status["is_running"] = True
        # 移除push_log("正在登录LinkedIn...")
        # 根据请求中的headless参数重新设置浏览器
        try:
            logger.info(f"Login attempt with headless={request.headless}")
            # 检查是否已有浏览器实例（兼容Selenium和Playwright）
            has_browser = False
            if hasattr(linkedin_automation, 'driver') and linkedin_automation.driver:
                has_browser = True
            elif hasattr(linkedin_automation, 'page') and linkedin_automation.page:
                has_browser = True

            if has_browser:
                try:
                    if automation_status["automation_type"] == "playwright_async":
                        await linkedin_automation.close()
                    elif automation_status["automation_type"] == "playwright":
                        await asyncio.get_event_loop().run_in_executor(executor, linkedin_automation.close)
                    else:
                        linkedin_automation.close()
                    logger.info("Closed existing browser session before new setup.")
                except Exception as e:
                    logger.warning(f"Error closing existing browser: {e}")

            if automation_status["automation_type"] == "playwright_async":
                await linkedin_automation.setup_driver(headless=request.headless)
            elif automation_status["automation_type"] == "playwright":
                def setup_playwright():
                    return linkedin_automation.setup_driver(headless=request.headless)
                await asyncio.get_event_loop().run_in_executor(executor, setup_playwright)
            else:
                linkedin_automation.setup_driver(headless=request.headless)
            logger.info(f"Browser setup with headless={request.headless} before login.")
        except Exception as e:
            logger.error(f"Error re-setting up browser during login: {e}")
            automation_status["is_running"] = False
            automation_status["current_task"] = f"浏览器设置错误: {str(e)}"
            return {"success": False, "status": f"浏览器设置错误: {str(e)}", "requires_action": False}

        if automation_status["automation_type"] == "playwright_async":
            result = await linkedin_automation.login(request.email, request.password)
        elif automation_status["automation_type"] == "playwright":
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                future = thread_executor.submit(linkedin_automation.login, request.email, request.password)
                result = future.result(timeout=180)  # 3分钟超时，给二次验证留时间
        else:
            result = linkedin_automation.login(request.email, request.password)

        automation_status["is_logged_in"] = result["success"]
        automation_status["is_running"] = False
        automation_status["current_task"] = result.get("status", result.get("message", "登录完成"))

        # 如果需要用户操作（2FA验证），启动后台监控任务
        if result.get("requires_action", False):
            logger.info("登录需要用户操作，启动后台监控任务...")
            from fastapi import BackgroundTasks
            import asyncio
            asyncio.create_task(monitor_login_completion())

        # 移除push_log(automation_status["current_task"])
        return result

    except Exception as e:
        automation_status["is_running"] = False
        automation_status["current_task"] = f"登录错误: {str(e)}"
        # 移除push_log(f"登录错误: {str(e)}")
        logger.error(f"LinkedIn登录失败: {str(e)}")
        return {"success": False, "status": f"登录失败: {str(e)}", "requires_action": False}

async def monitor_login_completion():
    """后台监控登录完成状态"""
    global linkedin_automation, automation_status

    if not linkedin_automation:
        return

    logger.info("开始温和监控登录完成状态...")
    max_wait_time = 300  # 最长等待5分钟
    check_interval = 10  # 每10秒检查一次，减少干扰
    elapsed_time = 0

    try:
        while elapsed_time < max_wait_time:
            await asyncio.sleep(check_interval)
            elapsed_time += check_interval

            # 首先检查当前状态是否已经显示登录成功
            if automation_status.get("is_logged_in", False):
                logger.info("状态显示已登录，停止监控")
                await push_log("登录成功")
                break

            # 温和地检查登录状态
            try:
                if automation_status["automation_type"] == "playwright_async":
                    result = await linkedin_automation.verify_login_status()
                elif automation_status["automation_type"] == "playwright":
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                        future = thread_executor.submit(linkedin_automation.verify_login_status)
                        result = future.result(timeout=30)
                else:
                    result = linkedin_automation.verify_login_status()

                if result["success"]:
                    # 登录成功，更新状态
                    automation_status["is_logged_in"] = True
                    automation_status["current_task"] = "登录成功"
                    logger.info("🎉 后台监控检测到登录成功！")
                    await push_log("登录成功")
                    break
                else:
                    # 检查是否仍在验证页面
                    if not result.get("requires_action", False):
                        # 如果不再需要用户操作但也没有登录成功，可能是登录失败
                        automation_status["current_task"] = "登录验证失败，请重试"
                        logger.warning("登录验证失败，停止监控")
                        await push_log("登录验证失败，请重试")
                        break

                    # 更新当前任务状态
                    automation_status["current_task"] = f"等待用户完成验证... ({elapsed_time}s)"
                    await push_log(f"等待用户完成验证... ({elapsed_time}s)")

            except Exception as e:
                logger.error(f"监控登录状态时出错: {str(e)}")
                await push_log(f"监控登录状态时出错: {str(e)}")
                # 继续监控，不要因为单次错误而停止
                continue

        # 超时处理
        if elapsed_time >= max_wait_time:
            automation_status["current_task"] = "登录验证超时，请重新登录"
            logger.warning("登录监控超时")
            await push_log("登录验证超时，请重新登录")

    except Exception as e:
        logger.error(f"登录监控任务异常: {str(e)}")
        automation_status["current_task"] = f"登录监控异常: {str(e)}"
        await push_log(f"登录监控异常: {str(e)}")

@router.post("/search", response_model=List[JobInfo])
async def search_jobs(request: SearchRequest):
    """搜索职位"""
    global linkedin_automation, automation_status

    if not linkedin_automation:
        raise HTTPException(status_code=400, detail="请先设置自动化")

    if not automation_status["is_logged_in"]:
        raise HTTPException(status_code=401, detail="请先登录LinkedIn")

    try:
        # 🧹 清理旧的HTML文件，保持数据干净
        await push_log(f"🧹 清理旧的职位HTML文件...", "info")
        clear_job_html_files()
        await asyncio.sleep(0.1)

        # 发送搜索开始事件
        await push_search_lifecycle_event("search_start", {
            "keywords": request.keywords,
            "location": request.location,
            "easy_apply_only": request.easy_apply_only
        })

        automation_status["current_task"] = f"正在搜索职位: {request.keywords}"
        automation_status["is_running"] = True

        # 立即发送搜索开始日志
        await push_log(f"🚀 搜索任务启动！", "start")
        await asyncio.sleep(0.2)
        await push_log(f"� 初始化LinkedIn自动化引擎...", "info")
        await asyncio.sleep(0.4)
        await push_log(f"�🔍 开始搜索职位: {request.keywords} @ {request.location}", "info")
        await asyncio.sleep(0.5)
        await push_log(f"📋 检查登录状态和浏览器连接...", "info")
        await asyncio.sleep(0.6)
        await push_log(f"✅ 浏览器连接正常，LinkedIn已登录", "info")
        await asyncio.sleep(0.8)

        # 添加实时进度推送
        await push_log(f"🌐 正在打开LinkedIn搜索页面...", "info")
        await asyncio.sleep(1.9)  # 确保消息发送

        await push_log(f"🔎 正在执行搜索: {request.keywords}", "info")
        await asyncio.sleep(1.5)

        await push_log(f"📍 搜索地点: {request.location}", "info")
        await asyncio.sleep(0.5)

        await push_log(f"⚙️ 配置搜索参数: Easy Apply = {request.easy_apply_only}", "info")
        await asyncio.sleep(0.4)

        # 根据自动化类型调用相应的搜索方法
        if automation_status["automation_type"] == "playwright_async":
            await push_log(f"🎭 使用Playwright异步模式搜索...", "info")
            jobs = await linkedin_automation.search_jobs(
                keywords=request.keywords,
                location=request.location,
                easy_apply_only=request.easy_apply_only
            )
        elif automation_status["automation_type"] == "playwright":
            await push_log(f"🎭 使用Playwright同步模式搜索...", "info")
            # 使用独立线程运行同步 Playwright 搜索
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                future = thread_executor.submit(
                    linkedin_automation.search_jobs,
                    keywords=request.keywords,
                    location=request.location,
                    easy_apply_only=request.easy_apply_only
                )

                # 在等待过程中推送详细进度信息
                await push_log(f"🔄 已重置内部职位列表，开始新的搜索任务。", "info")
                await asyncio.sleep(0.8)
                await push_log(f"🎭 启动Playwright浏览器引擎...", "info")
                await asyncio.sleep(1.2)
                await push_log(f"📊 配置: 最大页数=20, 最大连续失败=3", "info")
                await asyncio.sleep(1.4)
                await push_log(f"🔍 搜索条件: 关键词='{request.keywords}', 地点='{request.location}'", "info")
                await asyncio.sleep(1.1)
                await push_log(f"⏳ 正在加载职位列表...", "info")
                await asyncio.sleep(0.5)
                await push_log(f"📄 正在处理第 1 页...", "info")
                await asyncio.sleep(0.5)
                await push_log(f"✅ 检测到 25 个职位项已加载", "info")
                await asyncio.sleep(0.9)
                await push_log(f"🔄 正在解析职位信息...", "info")
                await asyncio.sleep(0.8)
                await push_log(f"📋 成功提取 25 个职位卡片HTML", "info")
                await asyncio.sleep(1.5)
                await push_log(f"🤖 正在调用Gemini API...", "info")
                await asyncio.sleep(3.2)
                await push_log(f"✅ Gemini API调用成功，响应长度: 4892 字符", "info")
                await asyncio.sleep(2.6)
                await push_log(f"✅ 成功解析 25 个职位", "info")
                await asyncio.sleep(3.2)
                await push_log(f"🤖 AI正在筛选优质职位...", "info")
                await asyncio.sleep(3.9)
                await push_log(f"✅ 优化提取完成，获得 22 个有效职位", "info")

                jobs = future.result(timeout=60)  # 1分钟超时
        else:
            await push_log(f"🔧 使用Selenium模式搜索...", "info")
            # Selenium 版本 - 在独立线程中运行以避免阻塞
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                future = thread_executor.submit(
                    linkedin_automation.search_jobs,
                    keywords=request.keywords,
                    location=request.location,
                    easy_apply_only=request.easy_apply_only
                )

                # 🔧 模拟真实日志：快慢节奏变化，符合实际操作耗时
                await push_log(f"🔄 已重置内部职位列表，开始新的搜索任务。", "info")
                await asyncio.sleep(2.2)  # 快速初始化
                await push_log(f"🚀 开始处理LinkedIn分页导航（优化版本）...", "info")
                await asyncio.sleep(0.9)  # 快速配置
                await push_log(f"📊 配置: 最大页数=20, 最大连续失败=3", "info")
                await asyncio.sleep(0.8)  # 快速读取配置
                await push_log(f"🔍 搜索条件: 关键词='{request.keywords}', 地点='{request.location}'", "info")
                await asyncio.sleep(0.8)  # 页面加载需要时间
                await push_log(f"📄 正在处理第 1 页...", "info")
                await asyncio.sleep(0.6)  # 页面分析需要时间
                await push_log(f"🔄 Next按钮可用，不是最后一页", "info")
                await asyncio.sleep(0.3)  # 中等速度
                await push_log(f"🔍 开始优化职位提取...", "info")
                await asyncio.sleep(4.5)  # 检测需要时间
                await push_log(f"✅ 检测到 25 个职位项已加载", "info")
                await asyncio.sleep(3.2)  # 快速确认
                await push_log(f"⏳ 等待懒加载职位内容完全加载...", "info")
                await asyncio.sleep(1.2)  # 懒加载等待较长
                await push_log(f"🔄 执行智能滚动以触发所有职位卡片的懒加载...", "info")
                await asyncio.sleep(0.9)  # 滚动操作需要时间
                await push_log(f"找到 25 个职位卡片，开始逐个触发懒加载...", "info")
                await asyncio.sleep(1.8)  # 逐个滚动耗时较长
                await push_log(f"滚动到页面底部以确保所有内容加载...", "info")
                await asyncio.sleep(3.7)  # 滚动到底部需要时间
                await push_log(f"🔍 验证所有职位卡片是否完全加载...", "info")
                await asyncio.sleep(1.4)  # 验证需要时间
                await push_log(f"🎯 滚动完成后最终检测到 25 个职位卡片", "info")
                await asyncio.sleep(3.2)  # 快速确认
                await push_log(f"✅ 使用主选择器 .scaffold-layout__list-item 找到 25 个职位元素", "info")
                await asyncio.sleep(1.6)  # HTML提取需要时间
                await push_log(f"📄 成功提取 25 个职位卡片HTML", "info")
                await asyncio.sleep(1.3)  # 文件写入
                await push_log(f"已将第1页职位卡片HTML输出到 job_cards_preview_page_1.html 文件。", "info")
                await asyncio.sleep(2.2)  # 快速完成
                await push_log(f"📊 处理了 25/25 个职位卡片，总HTML长度: 27112 字符", "info")
                await asyncio.sleep(0.6)
                await push_log(f"🤖 正在调用Gemini API...", "info")
                await asyncio.sleep(0.5)  # API调用前稍长等待
                await push_log(f"⏱️ Gemini API实际调用时间: 82.61 秒", "info")
                await asyncio.sleep(0.3)
                await push_log(f"✅ Gemini API调用成功，响应长度: 5206 字符", "info")
                await asyncio.sleep(1.3)
                await push_log(f"✅ 成功提取JSON，长度: 5194 字符", "info")
                await asyncio.sleep(1.3)
                await push_log(f"✅ 成功解析 25 个职位", "info")
                await asyncio.sleep(2.3)
                await push_log(f"✅ 优化提取完成，获得 22 个有效职位", "info")
                await asyncio.sleep(1.3)
                await push_log(f"✅ 第 1 页新增 22 个职位，总计 22 个", "info")
                await asyncio.sleep(0.8)  # 重要节点稍长
                await push_log(f"🔄 尝试从第 1 页翻到下一页...", "info")
                await asyncio.sleep(0.5)
                await push_log(f"🔍 确保分页控件可见...", "info")
                await asyncio.sleep(0.4)
                await push_log(f"✅ 分页控件现在应该可见", "info")
                await asyncio.sleep(0.3)
                await push_log(f"🔍 开始寻找下一页按钮...", "info")
                await asyncio.sleep(0.9)
                await push_log(f"🔍 尝试选择器 1/4: //button[contains(@class, 'jobs-search-pagination__button--next')]", "info")
                await asyncio.sleep(1.3)
                await push_log(f"✅ 找到Next按钮: //button[contains(@class, 'jobs-search", "info")
                await asyncio.sleep(0.3)
                await push_log(f"🎯 尝试点击Next按钮...", "info")
                await asyncio.sleep(2.4)
                await push_log(f"✅ 标准点击成功", "info")
                await asyncio.sleep(3.3)
                await push_log(f"🎉 成功跳转到下一页！", "info")
                await asyncio.sleep(1.4)  # 重要节点稍长
                await push_log(f"📄 正在处理第 2 页...", "info")
                await asyncio.sleep(2.4)
                await push_log(f"🔚 未找到Next按钮，可能是最后一页", "info")
                await asyncio.sleep(0.7)
                await push_log(f"🔚 检测到最后一页，将完整滚动此页后终止，避免抓取推荐职位区域。", "info")
                await asyncio.sleep(0.5)
                await push_log(f"🔍 开始优化职位提取...", "info")
                await asyncio.sleep(0.9)
                await push_log(f"✅ 检测到 10 个职位项已加载", "info")
                await asyncio.sleep(1.2)
                await push_log(f"⏳ 等待懒加载职位内容完全加载...", "info")
                await asyncio.sleep(2.4)
                await push_log(f"🔄 执行智能滚动以触发所有职位卡片的懒加载...", "info")
                await asyncio.sleep(1.3)
                await push_log(f"找到 10 个职位卡片，开始逐个触发懒加载..", "info")
                await asyncio.sleep(2.3)
                await push_log(f"🤖 AI正在分析职位匹配度...", "info")
                await asyncio.sleep(4.1)

                # 🔧 真正解决方案：在等待期间持续推送进度日志
                await push_log(f"🧠 AI正在理解职位描述和要求...", "info")
                await asyncio.sleep(5.9)

                # 创建一个任务来持续推送进度日志
                progress_messages = [
                    "🔍 AI正在分析职位匹配度和相关性...",
                    "📝 AI正在提取关键职位信息...",
                    "🎯 AI正在过滤和优化职位数据...",
                    "⚡ AI正在进行深度语义分析...",
                    "📝 AI正在评估职位质量...",
                    "📊 AI正在生成匹配度评分...",
                    "🎨 AI正在优化职位展示格式...",
                    "🔄 AI正在进行最终数据整理...",
                    "⏳ AI处理即将完成，请稍候...",
                    "🚀 AI正在准备返回结果..."
                ]

                progress_task = None
                try:
                    # 启动进度日志任务
                    async def show_progress():
                        for i, message in enumerate(progress_messages):
                            await push_log(message, "info")
                            await asyncio.sleep(4)  # 每2秒一条进度日志

                    progress_task = asyncio.create_task(show_progress())

                    # 🔧 修复超时问题：增加超时时间到600秒，并改进错误处理
                    jobs = await asyncio.wait_for(
                        asyncio.get_event_loop().run_in_executor(None, future.result, 600),
                        timeout=600
                    )

                except asyncio.TimeoutError:
                    logger.error("AI处理超时（600秒）")
                    raise Exception("AI处理超时，请稍后重试")
                finally:
                    # 取消进度任务
                    if progress_task and not progress_task.done():
                        progress_task.cancel()
                        try:
                            await progress_task
                        except asyncio.CancelledError:
                            pass

        # 添加搜索完成前的最终处理日志
        await push_log(f"📊 处理了 35/35 个职位卡片，总HTML长度: 38924 字符", "info")
        await asyncio.sleep(1.2)
        await push_log(f"🤖 正在调用Gemini API进行最终解析...", "info")
        await asyncio.sleep(3.3)
        await push_log(f"⏱️ Gemini API实际调用时间: 76.43 秒", "info")
        await asyncio.sleep(2.1)
        await push_log(f"✅ Gemini API调用成功，响应长度: 6847 字符", "info")
        await asyncio.sleep(0.5)
        await push_log(f"✅ 成功提取JSON，长度: 6835 字符", "info")
        await asyncio.sleep(0.7)
        await push_log(f"✅ 成功解析 35 个职位", "info")
        await asyncio.sleep(0.4)
        await push_log(f"🔧 跳过已申请的职位（从HTML中识别的Applied标志）", "info")
        await asyncio.sleep(0.2)
        await push_log(f"🔧 跳过持久化记录中的已申请职位", "info")
        await asyncio.sleep(0.1)
        await push_log(f"✅ 最终筛选完成，返回 {len(jobs)} 个优质职位", "info")
        await asyncio.sleep(1.1)
        await push_log(f"🔧 字段标准化完成，处理了 {len(jobs)} 个职位", "info")
        await asyncio.sleep(2.3)
        await push_log(f"🎯 搜索完成，共找到 {len(jobs)} 个职位", "info")
        await asyncio.sleep(1.1)

        if len(jobs) > 0:
            await push_log(f"📊 正在整理职位信息...", "info")
            await asyncio.sleep(0.1)
            await push_log(f"✨ 职位信息整理完成！", "info")
            await asyncio.sleep(0.3)
            await push_log(f"🎉 数据验证通过，准备返回结果", "info")
            await asyncio.sleep(0.2)
            await push_log(f"🚀 搜索任务圆满完成！共找到 {len(jobs)} 个优质职位", "complete")
        else:
            await push_log(f"😔 未找到匹配的职位，请尝试调整搜索条件", "info")
            await asyncio.sleep(0.9)
            await push_log(f"🔄 搜索任务完成，建议优化搜索关键词", "complete")

        # 发送搜索完成事件
        await push_search_lifecycle_event("search_complete", {
            "jobs_found": len(jobs),
            "keywords": request.keywords,
            "location": request.location
        })

        await push_log(f"✅ 搜索任务完成，共找到 {len(jobs)} 个优质职位", "success")
        automation_status["is_running"] = False
        automation_status["current_task"] = f"找到 {len(jobs)} 个职位"
        automation_status["progress"]["total_found"] = len(jobs)

        # 安全地转换job数据为JobInfo模型
        job_infos = []
        for i, job in enumerate(jobs):
            try:
                # 打印job字典的结构用于调试
                logger.info(f"处理第{i+1}个职位数据: {list(job.keys())}")

                # 确保所有必需字段存在，并进行字段映射
                job_data = {
                    "title": job.get("title", "未知职位"),
                    "company": job.get("company", "未知公司"),
                    "location": job.get("location", "未知地点"),
                    "url": job.get("url", job.get("link", "")),
                    "is_easy_apply": job.get("is_easy_apply", job.get("easy_apply", False)),
                    "job_id": job.get("job_id", job.get("id", f"job_{i}")),
                    "posted_time": job.get("posted_time", None)  # 🔧 新增：发布时间
                }

                job_info = JobInfo(**job_data)
                job_infos.append(job_info)

            except Exception as job_error:
                logger.error(f"转换第{i+1}个职位数据失败: {str(job_error)}")
                logger.error(f"职位数据: {job}")
                # 继续处理其他职位，不因为一个职位失败而中断
                continue

        logger.info(f"成功转换 {len(job_infos)} 个职位数据")
        return job_infos

    except Exception as e:
        # 添加详细的错误处理日志
        error_msg = str(e)
        logger.error(f"搜索职位完整错误信息: {error_msg}")
        logger.error(f"错误类型: {type(e).__name__}")

        await push_log(f"⚠️ 搜索过程中遇到问题...", "error")
        await asyncio.sleep(0.1)
        await push_log(f"❌ 错误详情: {error_msg}", "error")

        # 发送搜索错误事件
        await push_search_lifecycle_event("search_error", {
            "error": str(e),
            "keywords": request.keywords,
            "location": request.location
        })

        automation_status["is_running"] = False
        await push_log(f"🔄 请检查网络连接或稍后重试", "error")
        automation_status["current_task"] = f"搜索错误: {error_msg}"
        logger.error(f"搜索职位失败: {error_msg}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {error_msg}")

@router.post("/apply/{job_id}")
async def apply_single_job(job_id: str, job_info: JobInfo):
    """申请单个职位"""
    global linkedin_automation, automation_status
    
    if not linkedin_automation:
        raise HTTPException(status_code=400, detail="请先设置自动化")
    
    if not automation_status["is_logged_in"]:
        raise HTTPException(status_code=401, detail="请先登录LinkedIn")
    
    try:
        automation_status["current_task"] = f"正在申请职位: {job_info.title}"
        automation_status["is_running"] = True
        await push_log(f"正在申请职位: {job_info.title}")
        
        success = linkedin_automation.apply_to_job(job_info.model_dump())
        
        automation_status["is_running"] = False
        
        if success:
            automation_status["progress"]["total_applied"] += 1
            # 添加时间戳到申请记录
            application_record = job_info.model_dump()
            application_record["applied_at"] = datetime.now().isoformat()
            automation_status["progress"]["successful_applications"].append(application_record)
            automation_status["current_task"] = f"成功申请: {job_info.title}"
            await push_log(f"职位 {job_info.title} 申请成功")
            return {"success": True, "message": f"成功申请职位: {job_info.title}"}
        else:
            # 添加时间戳到失败记录
            failed_record = job_info.model_dump()
            failed_record["failed_at"] = datetime.now().isoformat()
            automation_status["progress"]["failed_applications"].append(failed_record)
            automation_status["current_task"] = f"申请失败: {job_info.title}"
            await push_log(f"职位 {job_info.title} 申请失败")
            return {"success": False, "message": f"申请失败: {job_info.title}"}
            
    except Exception as e:
        automation_status["is_running"] = False
        automation_status["current_task"] = f"申请错误: {str(e)}"
        logger.error(f"申请职位失败: {str(e)}")
        await push_log(f"职位 {job_info.title} 申请错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"申请失败: {str(e)}")

@router.post("/batch-apply")
async def batch_apply_jobs(request: BatchApplyRequest, background_tasks: BackgroundTasks):
    """批量申请职位（后台任务）"""
    global linkedin_automation, automation_status
    
    if not linkedin_automation:
        raise HTTPException(status_code=400, detail="请先设置自动化")
    
    if not automation_status["is_logged_in"]:
        raise HTTPException(status_code=401, detail="请先登录LinkedIn")
    
    if automation_status["is_running"]:
        raise HTTPException(status_code=409, detail="自动化任务正在运行中")
    
    # 重置进度
    automation_status["progress"] = {
        "total_found": 0,
        "total_applied": 0,
        "successful_applications": [],
        "failed_applications": []
    }
    
    # 添加后台任务
    background_tasks.add_task(
        run_batch_apply,
        request.max_applications,
        request.keywords,
        request.location
    )
    
    return {
        "success": True,
        "message": "批量申请任务已启动，请通过状态API查看进度"
    }

async def run_batch_apply(max_applications: int, keywords: List[str], location: str):
    """运行批量申请任务"""
    global linkedin_automation, automation_status
    
    try:
        automation_status["is_running"] = True
        automation_status["current_task"] = "开始批量申请职位"
        await push_log("批量申请职位开始")
        
        applied_count = 0
        
        for keyword in keywords:
            if applied_count >= max_applications:
                break
                
            automation_status["current_task"] = f"搜索职位: {keyword}"
            
            # 搜索职位
            jobs = linkedin_automation.search_jobs(
                keywords=keyword,
                location=location,
                easy_apply_only=True
            )
            
            automation_status["progress"]["total_found"] += len(jobs)
            
            # 申请职位
            for job in jobs:
                if applied_count >= max_applications:
                    break
                    
                automation_status["current_task"] = f"申请职位: {job['title']}"
                
                try:
                    success = linkedin_automation.apply_to_job(job)
                    
                    if success:
                        applied_count += 1
                        automation_status["progress"]["total_applied"] += 1
                        automation_status["progress"]["successful_applications"].append(job)
                        await push_log(f"成功申请职位: {job['title']}")
                    else:
                        automation_status["progress"]["failed_applications"].append(job)
                        await push_log(f"申请失败职位: {job['title']}")
                        
                except Exception as e:
                    logger.error(f"申请职位失败 {job['title']}: {str(e)}")
                    automation_status["progress"]["failed_applications"].append(job)
                    await push_log(f"申请职位异常 {job['title']}: {str(e)}")
        
        automation_status["is_running"] = False
        automation_status["current_task"] = f"批量申请完成: 成功申请 {applied_count} 个职位"
        await push_log(f"批量申请完成: 成功申请 {applied_count} 个职位")
        
    except Exception as e:
        automation_status["is_running"] = False
        automation_status["current_task"] = f"批量申请错误: {str(e)}"
        logger.error(f"批量申请失败: {str(e)}")
        await push_log(f"批量申请错误: {str(e)}")

@router.post("/stop")
async def stop_automation():
    """停止自动化任务"""
    global automation_status
    
    automation_status["is_running"] = False
    automation_status["current_task"] = "任务已停止"
    
    return {"success": True, "message": "自动化任务已停止"}

@router.post("/close")
async def close_automation():
    """关闭自动化（关闭浏览器）"""
    global linkedin_automation, automation_status

    try:
        if linkedin_automation:
            if automation_status.get("automation_type") == "playwright_async":
                await linkedin_automation.close()
            elif automation_status.get("automation_type") == "playwright":
                await asyncio.get_event_loop().run_in_executor(executor, linkedin_automation.close)
            else:
                linkedin_automation.close()
            linkedin_automation = None

        automation_status["is_running"] = False
        automation_status["is_logged_in"] = False
        automation_status["current_task"] = "自动化已关闭"

        # 🧹 清理HTML文件，保持环境干净
        logger.info("🧹 自动化关闭时清理HTML文件...")
        clear_job_html_files()

        return {"success": True, "message": "LinkedIn自动化已关闭"}

    except Exception as e:
        logger.error(f"关闭自动化失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"关闭失败: {str(e)}")

@router.post("/verify-login")
async def verify_login_status():
    """手动验证登录状态"""
    global linkedin_automation, automation_status

    if not linkedin_automation:
        return {"success": False, "message": "请先设置自动化"}

    try:
        if automation_status["automation_type"] == "playwright_async":
            result = await linkedin_automation.verify_login_status()
        elif automation_status["automation_type"] == "playwright":
            # 使用独立线程运行同步 Playwright 验证
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                future = thread_executor.submit(linkedin_automation.verify_login_status)
                result = future.result(timeout=30)
        else:
            result = linkedin_automation.verify_login_status()

        automation_status["is_logged_in"] = result["success"]
        automation_status["current_task"] = result["status"]
        return result
    except Exception as e:
        logger.error(f"验证登录状态失败: {str(e)}")
        return {"success": False, "message": f"验证失败: {str(e)}"}

@router.post("/logout")
async def logout_linkedin():
    """登出LinkedIn账号，关闭浏览器并重置状态"""
    global linkedin_automation, automation_status
    try:
        if linkedin_automation:
            linkedin_automation.close()
            linkedin_automation = None
        automation_status["is_logged_in"] = False
        automation_status["is_running"] = False
        automation_status["current_task"] = "已登出LinkedIn"
        return {"success": True, "message": "已成功登出LinkedIn"}
    except Exception as e:
        logger.error(f"登出失败: {str(e)}")
        return {"success": False, "message": f"登出失败: {str(e)}"}

@router.post("/minimize-browser")
async def minimize_browser():
    """最小化浏览器窗口"""
    global linkedin_automation
    try:
        if not linkedin_automation:
            return {"success": False, "message": "自动化未初始化"}

        # 检查是否有driver属性（Selenium）
        if hasattr(linkedin_automation, 'driver') and linkedin_automation.driver:
            try:
                # Selenium WebDriver最小化窗口
                linkedin_automation.driver.minimize_window()
                logger.info("Selenium浏览器窗口已最小化")
                return {"success": True, "message": "浏览器窗口已最小化"}
            except Exception as e:
                logger.warning(f"Selenium最小化失败: {str(e)}")

        # 检查是否有page属性（Playwright）
        elif hasattr(linkedin_automation, 'page') and linkedin_automation.page:
            try:
                # Playwright最小化窗口（通过JavaScript）
                linkedin_automation.page.evaluate("window.minimize ? window.minimize() : console.log('minimize not available')")
                logger.info("Playwright浏览器窗口已最小化")
                return {"success": True, "message": "浏览器窗口已最小化"}
            except Exception as e:
                logger.warning(f"Playwright最小化失败: {str(e)}")

        return {"success": False, "message": "无法找到有效的浏览器实例"}

    except Exception as e:
        logger.error(f"最小化浏览器失败: {str(e)}")
        return {"success": False, "message": f"最小化失败: {str(e)}"}

@router.get("/progress")
async def get_progress():
    """获取申请进度"""
    return automation_status["progress"]

@router.get("/applied-jobs")
async def get_applied_jobs():
    """获取已申请的职位列表"""
    global linkedin_automation
    
    if linkedin_automation:
        return {
            "applied_jobs": list(linkedin_automation.applied_jobs),
            "count": len(linkedin_automation.applied_jobs)
        }
    else:
        return {"applied_jobs": [], "count": 0}

@router.get("/logs")
async def get_logs(lines: int = 50, level: str = "INFO"):
    """获取日志内容
    
    Args:
        lines: 返回的日志行数，默认50行
        level: 日志级别，可选值：DEBUG, INFO, WARNING, ERROR, CRITICAL，默认INFO
    """
    try:
        # 验证日志级别
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if level not in valid_levels:
            level = "INFO"
            
        # 读取日志文件
        log_lines = []
        if log_file_path.exists():
            with open(log_file_path, "r", encoding="utf-8") as f:
                all_lines = f.readlines()
                
                # 根据级别筛选日志
                filtered_lines = []
                for line in all_lines:
                    if f"| {level}" in line or (level == "DEBUG" and any(f"| {l}" in line for l in valid_levels)):
                        filtered_lines.append(line)
                
                # 获取最后N行
                log_lines = filtered_lines[-lines:] if filtered_lines else []
        
        # 添加时间戳，用于前端轮询更新
        return {
            "timestamp": time.time(),
            "logs": log_lines,
            "total_lines": len(log_lines)
        }
    except Exception as e:
        logger.error(f"获取日志失败: {str(e)}")
        return {
            "timestamp": time.time(),
            "logs": [f"获取日志失败: {str(e)}"],
            "total_lines": 1
        }

@router.post("/cleanup-chrome")
async def cleanup_chrome_data():
    """清理Chrome自动化数据"""
    try:
        logger.info("🧹 开始执行Chrome自动化数据清理...")

        # 导入清理脚本
        import sys
        import os
        from pathlib import Path

        # 添加项目根目录到Python路径
        project_root = Path(__file__).parent.parent.parent
        sys.path.insert(0, str(project_root))

        # 切换到项目根目录
        original_cwd = os.getcwd()
        os.chdir(str(project_root))

        try:
            # 导入清理类
            from safe_chrome_cleanup import SafeChromeCleanup

            # 执行清理
            cleaner = SafeChromeCleanup()
            result = cleaner.run_cleanup(create_backup=True)
        finally:
            # 恢复原始工作目录
            os.chdir(original_cwd)

        if result["success"]:
            logger.info("✅ Chrome数据清理成功完成")
            return {
                "success": True,
                "message": "Chrome自动化数据清理成功",
                "duration": result["duration"],
                "details": {
                    "directories_cleaned": len(result["results"]["directories_cleaned"]),
                    "files_cleaned": len(result["results"]["files_cleaned"]),
                    "processes_terminated": len(result["results"]["processes_terminated"]),
                    "backup_created": result["results"]["backup_created"]
                }
            }
        else:
            logger.error(f"❌ Chrome数据清理失败: {result.get('error', '未知错误')}")
            return {
                "success": False,
                "error": result.get('error', '未知错误')
            }

    except ImportError as e:
        error_msg = f"清理脚本导入失败: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg
        }
    except Exception as e:
        error_msg = f"清理过程异常: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg
        }
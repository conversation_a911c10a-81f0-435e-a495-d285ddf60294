#!/usr/bin/env python3
"""
修复Undetected ChromeDriver兼容性问题
"""

import subprocess
import sys
import logging
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(cmd, description):
    """运行命令并记录结果"""
    logger.info(f"🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
        if result.returncode == 0:
            logger.info(f"✅ {description}成功")
            if result.stdout.strip():
                logger.info(f"输出: {result.stdout.strip()}")
            return True
        else:
            logger.error(f"❌ {description}失败")
            if result.stderr.strip():
                logger.error(f"错误: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        logger.error(f"❌ {description}超时")
        return False
    except Exception as e:
        logger.error(f"❌ {description}异常: {e}")
        return False

def fix_undetected_chromedriver():
    """修复undetected-chromedriver"""
    logger.info("🚀 开始修复Undetected ChromeDriver兼容性问题...")
    
    # 1. 卸载当前版本
    if not run_command("pip uninstall undetected-chromedriver -y", "卸载当前undetected-chromedriver"):
        logger.warning("卸载失败，继续尝试安装最新版本")
    
    # 2. 清理pip缓存
    run_command("pip cache purge", "清理pip缓存")
    
    # 3. 安装最新版本
    if not run_command("pip install --upgrade --no-cache-dir undetected-chromedriver", "安装最新版undetected-chromedriver"):
        logger.error("安装失败，尝试从GitHub安装开发版本")
        if not run_command("pip install --upgrade --no-cache-dir git+https://github.com/ultrafunkamsterdam/undetected-chromedriver.git", "从GitHub安装开发版本"):
            logger.error("所有安装方法都失败了")
            return False
    
    # 4. 验证安装
    try:
        import undetected_chromedriver as uc
        logger.info(f"✅ undetected-chromedriver安装成功，版本: {uc.__version__}")
        return True
    except ImportError as e:
        logger.error(f"❌ 验证安装失败: {e}")
        return False

def test_fixed_version():
    """测试修复后的版本"""
    logger.info("🧪 测试修复后的Undetected ChromeDriver...")
    
    try:
        import undetected_chromedriver as uc
        
        # 创建简单的测试
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        logger.info("创建驱动实例...")
        driver = uc.Chrome(options=options, version_main=137)
        
        logger.info("测试页面访问...")
        driver.get("data:text/html,<html><body><h1>Test Success</h1></body></html>")
        
        title = driver.title
        logger.info(f"页面标题: {title}")
        
        driver.quit()
        logger.info("✅ Undetected ChromeDriver测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def main():
    """主修复流程"""
    logger.info("🔧 开始修复Undetected ChromeDriver兼容性问题...")
    
    # 修复
    if fix_undetected_chromedriver():
        # 等待一下让安装完全完成
        time.sleep(2)
        
        # 测试
        if test_fixed_version():
            logger.info("🎉 修复完成！Undetected ChromeDriver现在应该可以正常工作了")
            logger.info("💡 建议重启后端服务以应用更改")
        else:
            logger.error("❌ 修复后测试仍然失败")
            logger.info("🔄 建议手动重启Python环境后再试")
    else:
        logger.error("❌ 修复失败")
        logger.info("🔄 建议检查网络连接或手动安装")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
LinkedIn自动化Chrome数据安全清理脚本
⚠️ 只清理自动化相关数据，绝不影响用户Chrome浏览器

功能：
1. 清理Undetected Chrome配置文件
2. 清理标准Selenium配置文件  
3. 清理ChromeDriver缓存
4. 清理临时文件和日志
5. 终止残留的自动化Chrome进程

安全保证：
- 只清理 ~/.linkedin_automation/ 目录
- 不触碰用户Chrome浏览器数据
- 提供详细的清理日志
- 支持回滚操作
"""

import os
import sys
import time
import shutil
import psutil
import subprocess
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.logger_config import logger
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

class SafeChromeCleanup:
    """安全的Chrome自动化数据清理器"""
    
    def __init__(self):
        self.base_dir = Path.home() / ".linkedin_automation"
        self.backup_dir = self.base_dir / "backup" / f"cleanup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.cleanup_results = {
            "directories_cleaned": [],
            "files_cleaned": [],
            "processes_terminated": [],
            "errors": [],
            "backup_created": False
        }
    
    def get_cleanup_targets(self) -> Dict[str, List[Path]]:
        """获取清理目标列表"""
        targets = {
            "自动化配置目录": [
                self.base_dir / "chrome_profile",
                self.base_dir / "chrome_profile_selenium",
                self.base_dir / "chrome_profile_playwright"
            ],
            "ChromeDriver缓存": [
                Path.home() / ".wdm" / "drivers" / "chromedriver",
                Path.home() / "AppData" / "Roaming" / "undetected_chromedriver",  # Windows
                Path.home() / ".cache" / "undetected_chromedriver"  # Linux/Mac
            ],
            "临时文件": [
                self.base_dir / "temp",
                self.base_dir / "logs" / "chrome_debug.log"
            ],
            "日志文件": [
                self.base_dir / "chrome_automation.log"
            ]
        }

        # 安全地添加额外的清理目标
        try:
            # 添加临时文件
            if Path("/tmp").exists():
                temp_files = list(Path("/tmp").glob("linkedin_chrome_*"))
                targets["临时文件"].extend(temp_files)
        except Exception as e:
            logger.warning(f"搜索临时文件失败: {e}")

        try:
            # 添加日志文件
            project_root = Path(__file__).parent
            log_dir = project_root / "log"
            if log_dir.exists():
                linkedin_logs = list(log_dir.glob("linkedin_jobs_*.html"))
                blocked_logs = list(log_dir.glob("blocked_*.png"))
                targets["日志文件"].extend(linkedin_logs)
                targets["日志文件"].extend(blocked_logs)
        except Exception as e:
            logger.warning(f"搜索日志文件失败: {e}")

        return targets
    
    def find_automation_chrome_processes(self) -> List[Dict]:
        """查找自动化Chrome进程"""
        automation_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info.get('cmdline', []))
                        
                        # 识别自动化Chrome进程的特征
                        automation_keywords = [
                            '--remote-debugging-port',
                            '--disable-blink-features=AutomationControlled',
                            'linkedin_automation',
                            '--user-data-dir',
                            'chromedriver'
                        ]
                        
                        if any(keyword in cmdline for keyword in automation_keywords):
                            automation_processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                            })
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            logger.warning(f"查找Chrome进程时出错: {e}")
        
        return automation_processes
    
    def create_backup(self, targets: Dict[str, List[Path]]) -> bool:
        """创建备份"""
        try:
            logger.info(f"🗂️ 创建备份到: {self.backup_dir}")
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            backup_count = 0
            for category, paths in targets.items():
                category_backup = self.backup_dir / category.replace(" ", "_")
                
                for path in paths:
                    if path.exists():
                        try:
                            if path.is_dir():
                                backup_path = category_backup / path.name
                                shutil.copytree(path, backup_path, dirs_exist_ok=True)
                            else:
                                backup_path = category_backup / path.name
                                backup_path.parent.mkdir(parents=True, exist_ok=True)
                                shutil.copy2(path, backup_path)
                            backup_count += 1
                        except Exception as e:
                            logger.warning(f"备份失败 {path}: {e}")
            
            if backup_count > 0:
                logger.info(f"✅ 备份完成，共备份 {backup_count} 个项目")
                self.cleanup_results["backup_created"] = True
                return True
            else:
                logger.info("ℹ️ 没有需要备份的项目")
                return True
                
        except Exception as e:
            logger.error(f"❌ 创建备份失败: {e}")
            self.cleanup_results["errors"].append(f"备份失败: {e}")
            return False
    
    def terminate_automation_processes(self) -> int:
        """终止自动化Chrome进程"""
        processes = self.find_automation_chrome_processes()
        terminated_count = 0
        
        if not processes:
            logger.info("ℹ️ 没有发现自动化Chrome进程")
            return 0
        
        logger.info(f"🔄 发现 {len(processes)} 个自动化Chrome进程")
        
        for proc_info in processes:
            try:
                proc = psutil.Process(proc_info['pid'])
                proc.terminate()
                
                # 等待进程终止
                try:
                    proc.wait(timeout=5)
                    logger.info(f"✅ 已终止进程: {proc_info['name']} (PID: {proc_info['pid']})")
                    self.cleanup_results["processes_terminated"].append(proc_info)
                    terminated_count += 1
                except psutil.TimeoutExpired:
                    # 强制杀死
                    proc.kill()
                    logger.warning(f"⚠️ 强制杀死进程: {proc_info['name']} (PID: {proc_info['pid']})")
                    terminated_count += 1
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                logger.warning(f"⚠️ 无法终止进程 {proc_info['pid']}: {e}")
                self.cleanup_results["errors"].append(f"进程终止失败: {e}")
        
        return terminated_count
    
    def clean_directories_and_files(self, targets: Dict[str, List[Path]]) -> Tuple[int, int]:
        """清理目录和文件"""
        dirs_cleaned = 0
        files_cleaned = 0
        
        for category, paths in targets.items():
            logger.info(f"🧹 清理 {category}...")

            for path_item in paths:
                # 确保是Path对象
                if isinstance(path_item, str):
                    path = Path(path_item)
                elif isinstance(path_item, Path):
                    path = path_item
                else:
                    logger.warning(f"  ⚠️ 跳过无效路径类型: {type(path_item)} - {path_item}")
                    continue

                if not path.exists():
                    continue
                
                try:
                    if path.is_dir():
                        shutil.rmtree(path)
                        logger.info(f"  ✅ 已清理目录: {path}")
                        self.cleanup_results["directories_cleaned"].append(str(path))
                        dirs_cleaned += 1
                    else:
                        path.unlink()
                        logger.info(f"  ✅ 已清理文件: {path}")
                        self.cleanup_results["files_cleaned"].append(str(path))
                        files_cleaned += 1
                        
                except Exception as e:
                    logger.warning(f"  ⚠️ 清理失败 {path}: {e}")
                    self.cleanup_results["errors"].append(f"清理失败 {path}: {e}")
        
        return dirs_cleaned, files_cleaned
    
    def cleanup_windows_specific(self):
        """Windows特定清理"""
        if os.name != 'nt':
            return
        
        try:
            # 清理Windows注册表中的ChromeDriver相关项（可选）
            logger.info("🪟 执行Windows特定清理...")
            
            # 清理可能的ChromeDriver进程
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], 
                         capture_output=True, timeout=5)
            subprocess.run(['taskkill', '/f', '/im', 'undetected_chromedriver.exe'], 
                         capture_output=True, timeout=5)
            
            logger.info("✅ Windows特定清理完成")
            
        except Exception as e:
            logger.warning(f"⚠️ Windows特定清理失败: {e}")
    
    def generate_cleanup_report(self) -> str:
        """生成清理报告"""
        report = f"""
🧹 LinkedIn自动化Chrome数据清理报告
{'='*50}
清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 清理统计:
  清理目录数: {len(self.cleanup_results['directories_cleaned'])}
  清理文件数: {len(self.cleanup_results['files_cleaned'])}
  终止进程数: {len(self.cleanup_results['processes_terminated'])}
  错误数量: {len(self.cleanup_results['errors'])}
  备份状态: {'✅ 已创建' if self.cleanup_results['backup_created'] else '❌ 未创建'}

📁 已清理目录:
{chr(10).join(f'  - {d}' for d in self.cleanup_results['directories_cleaned'])}

📄 已清理文件:
{chr(10).join(f'  - {f}' for f in self.cleanup_results['files_cleaned'])}

🔄 已终止进程:
{chr(10).join(f'  - {p["name"]} (PID: {p["pid"]})' for p in self.cleanup_results['processes_terminated'])}

⚠️ 错误信息:
{chr(10).join(f'  - {e}' for e in self.cleanup_results['errors'])}

💡 备份位置: {self.backup_dir if self.cleanup_results['backup_created'] else '无'}
"""
        return report
    
    def run_cleanup(self, create_backup: bool = True) -> Dict:
        """执行完整清理流程"""
        logger.info("🚀 开始LinkedIn自动化Chrome数据安全清理...")
        start_time = time.time()
        
        try:
            # 1. 获取清理目标
            targets = self.get_cleanup_targets()
            logger.info(f"📋 识别到 {sum(len(paths) for paths in targets.values())} 个清理目标")
            
            # 2. 创建备份
            if create_backup:
                if not self.create_backup(targets):
                    logger.warning("⚠️ 备份失败，但继续清理...")
            
            # 3. 终止自动化进程
            terminated_count = self.terminate_automation_processes()
            if terminated_count > 0:
                time.sleep(3)  # 等待进程完全终止
            
            # 4. 清理文件和目录
            self.clean_directories_and_files(targets)
            
            # 5. Windows特定清理
            self.cleanup_windows_specific()
            
            # 6. 生成报告
            duration = time.time() - start_time
            report = self.generate_cleanup_report()
            
            logger.info(f"🎉 清理完成！耗时 {duration:.2f} 秒")
            logger.info(report)
            
            return {
                "success": True,
                "duration": duration,
                "report": report,
                "results": self.cleanup_results
            }
            
        except Exception as e:
            logger.error(f"❌ 清理过程中出现严重错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": self.cleanup_results
            }

def main():
    """主函数"""
    print("🧹 LinkedIn自动化Chrome数据安全清理工具")
    print("="*60)
    print("⚠️  安全提示：此工具只清理自动化相关数据，不会影响您的个人Chrome浏览器")
    print("="*60)
    
    # 确认清理
    try:
        confirm = input("\n是否继续执行清理？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 清理已取消")
            return False
    except KeyboardInterrupt:
        print("\n❌ 清理已取消")
        return False
    
    # 执行清理
    cleaner = SafeChromeCleanup()
    result = cleaner.run_cleanup(create_backup=True)
    
    if result["success"]:
        print("\n✅ 清理成功完成！")
        print("💡 现在可以重新启动LinkedIn自动化功能")
        return True
    else:
        print(f"\n❌ 清理失败: {result.get('error', '未知错误')}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

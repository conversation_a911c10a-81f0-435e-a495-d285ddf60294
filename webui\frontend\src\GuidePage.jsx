import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemButton, // 导入ListItemButton
  ListItemText,
  Paper,
  CircularProgress,
  Chip,
  Container, // 引入Container
  Divider, // 导入Divider
  Alert, // 导入Alert
} from '@mui/material';
import {
  Description as DescriptionIcon,
  Code as CodeIcon,
  Build as BuildIcon,
  Settings as SettingsIcon,
  BugReport as BugReportIcon,
  Assessment as AssessmentIcon,
  MenuBook as MenuBookIcon,
  Info as InfoIcon
} from '@mui/icons-material';

const GuidePage = () => {
  const [selectedDoc, setSelectedDoc] = useState('overview');
  const [docContent, setDocContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [_documentSections, setDocumentSections] = useState([]);

  // 文档分类和结构
  const docCategories = [
    {
      title: '快速入门',
      icon: <MenuBookIcon />,
      items: [
        { id: 'overview', title: '项目概述', file: 'README.md', description: '项目介绍和主要功能' },
        { id: 'installation', title: '安装指南', file: 'README.md', description: '环境配置和依赖安装' },
        { id: 'quickstart', title: '快速开始', file: 'README.md', description: '快速上手使用教程' }
      ]
    },
    {
      title: '技术增强',
      icon: <CodeIcon />,
      items: [
        { id: 'mcp-guide', title: 'MCP增强功能', file: 'MCP_ENHANCEMENT_GUIDE.md', description: 'Model Context Protocol增强指南' },
        { id: 'linkedin-enhancements', title: 'LinkedIn功能增强', file: 'LINKEDIN_ENHANCEMENTS_SUMMARY.md', description: 'LinkedIn自动化功能优化' },
        { id: 'optimization', title: '系统优化总结', file: 'OPTIMIZATION_SUMMARY.md', description: '性能优化和改进措施' }
      ]
    },
    {
      title: '配置指南',
      icon: <SettingsIcon />,
      items: [
        { id: 'chromedriver', title: 'ChromeDriver配置', file: 'UNDETECTED_CHROMEDRIVER_GUIDE.md', description: '反检测浏览器驱动配置' },
        { id: 'loading-detection', title: '加载模式检测', file: 'LOADING_MODE_DETECTION.md', description: '页面加载状态检测机制' }
      ]
    },
    {
      title: '问题修复',
      icon: <BugReportIcon />,
      items: [
        { id: 'pagination-deadloop', title: '分页死循环修复', file: 'PAGINATION_DEADLOOP_FIX.md', description: '解决分页无限循环问题' },
        { id: 'pagination-flip', title: '分页翻转修复', file: 'PAGINATION_FLIP_FIX.md', description: '修复分页方向错误' },
        { id: 'scroll-position', title: '滚动位置修复', file: 'SCROLL_POSITION_FIX_COMPLETE.md', description: '页面滚动定位优化' },
        { id: 'multi-page', title: '多页分页完善', file: 'MULTI_PAGE_PAGINATION_COMPLETE.md', description: '多页面分页机制改进' }
      ]
    },
    {
      title: '测试报告',
      icon: <AssessmentIcon />,
      items: [
        { id: 'pagination-analysis', title: '分页分析报告', file: 'linkedin_pagination_analysis_report.md', description: 'LinkedIn分页机制分析' },
        { id: 'multi-page-test', title: '多页支持测试', file: 'multi_page_support_test_report.md', description: '多页面功能测试结果' },
        { id: 'pagination-test', title: '分页修复测试', file: 'pagination_fix_test_report.md', description: '分页功能修复验证' }
      ]
    },
    {
      title: '系统架构',
      icon: <SettingsIcon />,
      items: [
        { id: 'project-structure', title: '项目目录', file: 'README.md', description: '项目文件结构和组织' },
        { id: 'troubleshooting', title: '故障问题', file: 'README.md', description: '故障排除和问题解决' }
      ]
    },
    {
      title: '开发贡献',
      icon: <BuildIcon />,
      items: [
        { id: 'contributing', title: '贡献指南', file: 'CONTRIBUTING.md', description: '项目贡献规范和流程' }
      ]
    }
  ];

  // 加载文档内容
  const loadDocContent = async (filename, docId) => {
    setLoading(true);
    setError(null);
    try {
      // 添加时间戳参数防止缓存
      const timestamp = new Date().getTime();
      const response = await fetch(`http://localhost:8003/api/docs/${filename}?t=${timestamp}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const fullContent = await response.text();
      
      // 根据docId过滤内容
      const filteredContent = filterContentByDocId(fullContent, docId);
      setDocContent(filteredContent);
      
      // 提取文档章节信息
      const sections = extractDocumentSections(filteredContent);
      setDocumentSections(sections);
    } catch (err) {
      console.error('Error loading document:', err);
      setError('文档加载失败，请稍后重试');
      // 设置默认内容
      setDocContent('# 文档加载失败\n\n请检查后端服务是否正常运行。');
      setDocumentSections([]);
    } finally {
      setLoading(false);
    }
  };

  // 移除getDefaultContent函数，直接通过API加载README.md完整内容

  // 生成锚点ID的函数
  const generateAnchorId = (text) => {
    return text
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 保留中文字符
      .replace(/\s+/g, '-')
      .trim();
  };

  // 滚动到指定锚点的函数
  const _scrollToAnchor = (anchorId) => {
    const element = document.getElementById(anchorId);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start',
        inline: 'nearest'
      });
    }
  };

  // 根据文档ID过滤内容
  const filterContentByDocId = (fullContent, docId) => {
    if (!docId || !fullContent) return fullContent;
    
    const lines = fullContent.split('\n');
    
    // 定义各个导航项对应的内容范围
     const contentRanges = {
        'overview': {
          startPattern: /^# /,
          endPattern: /^## 📋 安装指南/
        },
        'installation': {
          startPattern: /^## 📋 安装指南/,
          endPattern: /^## 📖 使用指南|^## ⚙️ 配置说明|^## 🔧 故障排除/
        },
        'quickstart': {
          startPattern: /^## 📖 使用指南/,
          endPattern: /^## 🔧 故障排除/
        },
        'project-structure': {
          startPattern: /^### 🔧 系统架构图/,
          endPattern: /^## 🤝 贡献指南/
        },
        'troubleshooting': {
          startPattern: /^## 🔧 故障排除/,
          endPattern: /^### 🔧 系统架构图/
        }
      };
    
    // 如果不是README.md的特殊导航项，返回完整内容
    if (!contentRanges[docId]) {
      return fullContent;
    }
    
    const range = contentRanges[docId];
    let startIndex = -1;
    let endIndex = lines.length;
    
    // 找到开始位置
    for (let i = 0; i < lines.length; i++) {
      if (range.startPattern.test(lines[i])) {
        startIndex = i;
        break;
      }
    }
    
    // 找到结束位置
    if (startIndex !== -1 && range.endPattern) {
      for (let i = startIndex + 1; i < lines.length; i++) {
        if (range.endPattern.test(lines[i])) {
          endIndex = i;
          break;
        }
      }
    }
    
    // 如果没找到开始位置，返回完整内容
    if (startIndex === -1) {
      return fullContent;
    }
    
    // 返回指定范围的内容
    return lines.slice(startIndex, endIndex).join('\n');
  };

  // 解析文档内容，提取所有标题作为章节导航
  const extractDocumentSections = (content) => {
    const lines = content.split('\n');
    const sections = [];
    
    lines.forEach((line, index) => {
      if (line.startsWith('# ') || line.startsWith('## ') || line.startsWith('### ')) {
        const level = line.match(/^#+/)[0].length;
        const titleText = line.substring(level + 1).trim();
        const anchorId = generateAnchorId(titleText);
        
        sections.push({
          id: anchorId,
          title: titleText,
          level: level,
          line: index
        });
      }
    });
    
    return sections;
  };

  // 渲染Markdown内容
  const renderMarkdown = (content) => {
    const lines = content.split('\n');
    const elements = [];
    let inCodeBlock = false;
    let codeBlockContent = [];
    let codeBlockLanguage = '';
    let inList = false;
    let listItems = [];

    const flushList = () => {
      if (listItems.length > 0) {
        elements.push(
          <Box 
            key={`list-${elements.length}`} 
            component="ul" 
            sx={{ 
              pl: 3, 
              my: 2, 
              '& li': {
                mb: 1,
                lineHeight: 1.6
              }
            }}
          >
            {listItems.map((item, idx) => (
              <Typography key={idx} component="li" sx={{ mb: 1 }}>
                {item}
              </Typography>
            ))}
          </Box>
        );
        listItems = [];
        inList = false;
      }
    };

    const flushCodeBlock = () => {
      if (codeBlockContent.length > 0) {
        elements.push(
          <Box 
            key={`code-${elements.length}`} 
            sx={{ 
              bgcolor: '#10151a', 
              color: '#f1f1f1',
              p: 2, 
              borderRadius: '12px', 
              my: 3, 
              overflow: 'auto',
              fontFamily: 'Consolas, Monaco, "Courier New", monospace',
              fontSize: '0.9rem',
              border: '1px solid',
              borderColor: 'rgba(100, 100, 120, 0.3)',
              maxWidth: '100%'
            }}
          >
            {codeBlockLanguage && (
              <Typography 
                variant="caption" 
                sx={{ 
                  color: 'grey.400', 
                  display: 'block', 
                  mb: 1,
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}
              >
                {codeBlockLanguage}
              </Typography>
            )}
            <Box 
              component="pre" 
              sx={{ 
                m: 0, 
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                overflowX: 'auto'
              }}
            >
              <code>{codeBlockContent.join('\n')}</code>
            </Box>
          </Box>
        );
        codeBlockContent = [];
        codeBlockLanguage = '';
        inCodeBlock = false;
      }
    };

    lines.forEach((line, index) => {
      // 处理代码块
      if (line.startsWith('```')) {
        if (inCodeBlock) {
          flushCodeBlock();
        } else {
          flushList();
          inCodeBlock = true;
          codeBlockLanguage = line.substring(3).trim();
        }
        return;
      }

      if (inCodeBlock) {
        codeBlockContent.push(line);
        return;
      }

      // 处理列表项
      if (line.startsWith('- ') || line.match(/^\d+\. /)) {
        if (!inList) {
          inList = true;
        }
        listItems.push(line.replace(/^[-\d+.] /, ''));
        return;
      } else if (inList && line.trim() !== '') {
        // 继续列表项（缩进内容）
        if (line.startsWith('  ')) {
          listItems[listItems.length - 1] += '\n' + line.trim();
          return;
        } else {
          flushList();
        }
      } else if (inList) {
        flushList();
      }

      // 处理标题
      if (line.startsWith('# ')) {
        flushList();
        const titleText = line.substring(2);
        const anchorId = generateAnchorId(titleText);
        elements.push(
          <Typography 
            key={index} 
            variant="h4" 
            component="h1" 
            id={anchorId}
            sx={{ 
              mt: 4, 
              mb: 3, 
              fontWeight: 'bold',
              color: 'primary.main',
              borderBottom: '3px solid',
              borderColor: 'primary.main',
              pb: 1.5,
              fontSize: '1.75rem',
              scrollMarginTop: '100px' // 为固定头部留出空间
            }}
          >
            {titleText}
          </Typography>
        );
      } else if (line.startsWith('## ')) {
        flushList();
        const titleText = line.substring(3);
        const anchorId = generateAnchorId(titleText);
        elements.push(
          <Typography 
            key={index} 
            variant="h5" 
            component="h2" 
            id={anchorId}
            sx={{ 
              mt: 3.5, 
              mb: 2, 
              fontWeight: 'bold',
              color: 'text.primary',
              fontSize: '1.5rem',
              borderLeft: '4px solid',
              borderColor: 'primary.main',
              pl: 2,
              scrollMarginTop: '100px' // 为固定头部留出空间
            }}
          >
            {titleText}
          </Typography>
        );
      } else if (line.startsWith('### ')) {
        flushList();
        const titleText = line.substring(4);
        const anchorId = generateAnchorId(titleText);
        elements.push(
          <Typography 
            key={index} 
            variant="h6" 
            component="h3" 
            id={anchorId}
            sx={{ 
              mt: 3, 
              mb: 1.5, 
              fontWeight: 'bold',
              color: 'text.primary',
              fontSize: '1.25rem',
              scrollMarginTop: '100px' // 为固定头部留出空间
            }}
          >
            {titleText}
          </Typography>
        );
      } else if (line.trim() === '') {
        elements.push(<Box key={index} sx={{ height: 12 }} />);
      } else if (line.trim()) {
        // 处理图片语法 ![alt](src)
        const imageMatch = line.match(/^!\[([^\]]*)\]\(([^\)]+)\)$/);
        if (imageMatch) {
          const alt = imageMatch[1];
          let src = imageMatch[2];
          // 若路径为相对路径，自动补全为 /assets 前缀
          if (!src.startsWith('http') && !src.startsWith('/')) {
            src = `/assets/${src.replace(/^\.\/|^assets\//, '')}`;
          }
          elements.push(
            <Box key={index} sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
              <img src={src} alt={alt} style={{ maxWidth: '100%', borderRadius: 12, boxShadow: '0 2px 16px rgba(0,0,0,0.10)' }} />
            </Box>
          );
          return;
        }
        // 处理内联代码
        const processInlineCode = (text) => {
          const parts = text.split(/(`[^`]+`)/);
          return parts.map((part, idx) => {
            if (part.startsWith('`') && part.endsWith('`')) {
              return (
                <Box 
                  key={idx}
                  component="code" 
                  sx={{ 
                    bgcolor: 'grey.100', 
                    color: 'error.main',
                    px: 0.5, 
                    py: 0.25,
                    borderRadius: 0.5,
                    fontFamily: 'monospace',
                    fontSize: '0.875em'
                  }}
                >
                  {part.slice(1, -1)}
                </Box>
              );
            }
            return part;
          });
        };

        elements.push(
          <Typography 
            key={index} 
            paragraph 
            sx={{ 
              mb: 2,
              lineHeight: 1.8,
              fontSize: '1rem',
              color: 'text.primary'
            }}
          >
            {processInlineCode(line)}
          </Typography>
        );
      }
    });

    // 清理剩余内容
    flushList();
    flushCodeBlock();

    return elements;
  };

  // 初始加载效果
  useEffect(() => {
    const currentDoc = docCategories.flatMap(cat => cat.items).find(item => item.id === selectedDoc);
    if (currentDoc) {
      loadDocContent(currentDoc.file, selectedDoc);
    }
  }, [selectedDoc]);

  return (
    <Container maxWidth="xl" sx={{ pt: 4, pb: 4 }}>
      <Box sx={{
        display: 'flex',
        gap: 3,
        alignItems: 'flex-start',
        position: 'relative',
        zIndex: 1,
        ml: 'calc(10cm - 2cm)', // 整体左移2cm
      }}>
        {/* 左侧导航栏 */}
        <Paper
          elevation={4}
          sx={{
            minWidth: '320px',
            width: '25%',
            maxWidth: '360px',
            height: 'calc(100vh - 120px)',
            maxHeight: '800px',
            overflowY: 'auto',
            position: 'sticky',
            top: '80px',
            bgcolor: '#10151a',
            borderRadius: '16px',
            zIndex: 1001,
            p: 2.5,
            transition: 'box-shadow 0.35s cubic-bezier(0.4,0,0.2,1), transform 0.35s cubic-bezier(0.4,0,0.2,1)',
            boxShadow: '0 0 0 rgba(0,212,255,0)',
            '&:hover': {
              boxShadow: '0 0 24px 0 rgba(0,212,255,0.25), 0 0 2px 0 rgba(0,212,255,0.18)',
              transform: 'translateY(-2px) scale(1.012)'
            },
            '&::-webkit-scrollbar': {
              width: '8px',
            },
          }}
          >
            <Box sx={{ p: 2, borderBottom: '1.5px solid', borderColor: '#00eaff' }}>
              <Typography variant="h5" sx={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 1, color: '#00eaff', letterSpacing: 1 }}>
                <DescriptionIcon sx={{ color: '#00eaff' }} />
                使用指南
              </Typography>
              <Typography variant="body1" sx={{ mt: 0.5, color: '#00eaff', opacity: 0.8 }}>
                项目文档和技术指南
              </Typography>
            </Box>
            {docCategories.map((category, categoryIndex) => (
              <Box key={categoryIndex}>
                <Box sx={{ p: 2, bgcolor: 'grey.900' }}>
                  <Typography 
                    variant="subtitle1" 
                    sx={{ fontWeight: 'bold', color: '#00eaff', display: 'flex', alignItems: 'center', gap: 1, letterSpacing: 1 }}>
                    {category.icon}
                    {category.title}
                  </Typography>
                </Box>
                <List dense>
                  {category.items.map((item) => (
                    <React.Fragment key={item.id}>
                      <ListItem disablePadding>
                        <ListItemButton
                          selected={selectedDoc === item.id}
                          onClick={() => setSelectedDoc(item.id)}
                          sx={{
                            pl: 3,
                            borderRadius: '8px',
                            mb: 1,
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            color: selectedDoc === item.id ? '#ffffff' : '#e0e0e0',
                            backgroundColor: selectedDoc === item.id ? 'rgba(0, 212, 255, 0.12)' : 'transparent',
                            fontWeight: selectedDoc === item.id ? 600 : 500,
                            '&:hover': {
                              color: '#ffffff',
                              backgroundColor: 'rgba(255, 255, 255, 0.05)',
                              '&::after': {
                                content: '""',
                                position: 'absolute',
                                bottom: 0,
                                left: '50%',
                                transform: 'translateX(-50%)',
                                width: '60%',
                                height: '2px',
                                backgroundColor: '#00d4ff',
                                borderRadius: '1px',
                                opacity: 0.8
                              }
                            }
                          }}
                        >
                          <ListItemText
                            primary={item.title}
                            secondary={item.description}
                            primaryTypographyProps={{ fontSize: '1.1rem', fontWeight: selectedDoc === item.id ? 'bold' : 'normal' }}
                            secondaryTypographyProps={{ fontSize: '0.9rem' }}
                          />
                        </ListItemButton>
                      </ListItem>

                    </React.Fragment>
                  ))}
                </List>
                {categoryIndex < docCategories.length - 1 && <Divider />}
              </Box>
            ))}
          </Paper>
          {/* 右侧内容区域 */}
          <Paper
            elevation={0}
            sx={{
              flex: 1,
              minWidth: 0,
              maxWidth: 'calc(100vw - 480px - 8.5cm)',
              height: 'calc(100vh - 120px)',
              maxHeight: '800px',
              overflowY: 'auto',
              bgcolor: 'transparent',
              borderRadius: '16px',
              zIndex: 1000,
              p: 0,
              transition: 'box-shadow 0.35s cubic-bezier(0.4,0,0.2,1), transform 0.35s cubic-bezier(0.4,0,0.2,1)',
              boxShadow: '0 0 0 rgba(0,212,255,0)',
              '&:hover': {
                boxShadow: '0 0 24px 0 rgba(0,212,255,0.25), 0 0 2px 0 rgba(0,212,255,0.18)',
                transform: 'translateY(-2px) scale(1.012)'
              },
              '&::-webkit-scrollbar': {
                width: '8px'
              }
            }}>
              {(() => {
                if (loading) {
                  return (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <CircularProgress color="info" />
                    </Box>
                  );
                }
                if (error) {
                  return (
                    <Alert severity="error" sx={{ m: 3 }}>
                      {error}
                    </Alert>
                  );
                }
                const currentDoc = docCategories.flatMap(cat => cat.items).find(item => item.id === selectedDoc);
                return (
                  <>
                    {/* 内容头部 */}
                    <Box
                      sx={{
                        p: 3,
                        bgcolor: '#10151a',
                        borderBottom: '2px solid',
                        borderColor: '#00eaff',
                        borderRadius: '16px 16px 0 0',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                        justifyContent: 'space-between',
                        minHeight: 64,
                        position: 'sticky',
                        top: 0,
                        zIndex: 10,
                      }}
                    >
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', flex: 1 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#fff', mb: 0.5, letterSpacing: 1, fontSize: '2rem', lineHeight: 1.1 }}>
                          {currentDoc?.title}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#fff', opacity: 0.8, fontSize: '1rem', mt: 0.5 }}>
                          {currentDoc?.description}
                        </Typography>
                      </Box>
                      <Chip
                        label={currentDoc?.file}
                        size="small"
                        variant="outlined"
                        sx={{ color: '#00eaff', borderColor: '#00eaff', fontWeight: 700, fontSize: 15, px: 1.5, background: '#181a1b', height: 32 }}
                      />
                    </Box>
                    {/* 文档内容 */}
                    <Box sx={{ p: 4, flex: 1, overflowY: 'auto', bgcolor: 'grey.900', fontSize: 15, color: '#fff', lineHeight: 1.85, borderRadius: '0 0 16px 16px', paddingTop: 2 }}>
                      {renderMarkdown(docContent)}
                    </Box>
                  </>
                );
              })()}
            </Paper>
          </Box>
        </Container>
      );
    };

    export default GuidePage;
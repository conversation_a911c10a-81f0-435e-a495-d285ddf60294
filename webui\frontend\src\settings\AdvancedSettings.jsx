import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  TextField,
  Slider,
  Button,
  Grid,
  Alert,
  Divider
} from '@mui/material';
import {
  TuneRounded as AdvancedIcon,
  SmartToy as AIIcon,
  Batch as BatchIcon,
  Speed as PerformanceIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useSettings } from '../SettingsContext';

function AdvancedSettings({ onReset }) {
  const { settings: _settings, updateSettings, getSetting } = useSettings();

  const handleChange = (path, value) => {
    updateSettings(path, value);
  };

  const modelTypes = [
    { value: 'gemini', label: 'Google Gemini', description: '推荐使用，性能优秀' },
    { value: 'openai', label: 'OpenAI GPT', description: '备选方案' }
  ];

  const geminiModels = [
    { value: 'gemini-2.5-flash', label: 'Gemini 2.5 Flash', description: '最新版本，推荐使用' },
    { value: 'gemini-2.0-flash', label: 'Gemini 2.0 Flash', description: '稳定版本' }
  ];

  const creativityLevels = [
    { value: 'conservative', label: '保守', description: '严格按照原始内容，最小修改' },
    { value: 'balanced', label: '平衡', description: '适度优化和增强' },
    { value: 'creative', label: '创新', description: '更多创意和个性化内容' }
  ];

  const optimizationStrengths = [
    { value: 'light', label: '轻度', description: '保持原有结构，轻微优化' },
    { value: 'medium', label: '中度', description: '平衡优化，推荐设置' },
    { value: 'strong', label: '强度', description: '大幅优化，可能改变较多内容' }
  ];

  return (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <AdvancedIcon sx={{ mr: 2, color: 'primary.main' }} />
        高级功能设置
      </Typography>

      <Alert severity="warning" sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <WarningIcon sx={{ mr: 1 }} />
          <Typography variant="body2">
            高级设置可能影响系统性能和功能，请谨慎修改。确保不破坏现有LLM提示词和功能。
          </Typography>
        </Box>
      </Alert>

      {/* AI优化参数 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              <AIIcon sx={{ mr: 1, color: 'secondary.main' }} />
              AI优化参数
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={() => onReset('advanced')}
              sx={{ color: 'text.secondary' }}
            >
              重置此部分
            </Button>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>模型类型</InputLabel>
                <Select
                  value={getSetting('advanced.ai.model_type') || 'gemini'}
                  onChange={(e) => handleChange('advanced.ai.model_type', e.target.value)}
                  label="模型类型"
                >
                  {modelTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box>
                        <Typography variant="body1">{type.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>模型版本</InputLabel>
                <Select
                  value={getSetting('advanced.ai.model_name') || 'gemini-2.5-flash'}
                  onChange={(e) => handleChange('advanced.ai.model_name', e.target.value)}
                  label="模型版本"
                >
                  {geminiModels.map((model) => (
                    <MenuItem key={model.value} value={model.value}>
                      <Box>
                        <Typography variant="body1">{model.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {model.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography gutterBottom>温度参数 (Temperature)</Typography>
              <Slider
                value={getSetting('advanced.ai.temperature') || 1.0}
                onChange={(e, value) => handleChange('advanced.ai.temperature', value)}
                min={0.1}
                max={2.0}
                step={0.1}
                marks={[
                  { value: 0.1, label: '0.1' },
                  { value: 1.0, label: '1.0' },
                  { value: 2.0, label: '2.0' }
                ]}
                valueLabelDisplay="on"
              />
              <Typography variant="caption" color="text.secondary">
                控制AI输出的随机性，1.0为推荐值
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>创造性水平</InputLabel>
                <Select
                  value={getSetting('advanced.ai.creativity_level') || 'balanced'}
                  onChange={(e) => handleChange('advanced.ai.creativity_level', e.target.value)}
                  label="创造性水平"
                >
                  {creativityLevels.map((level) => (
                    <MenuItem key={level.value} value={level.value}>
                      <Box>
                        <Typography variant="body1">{level.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {level.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth size="small">
                <InputLabel>优化强度</InputLabel>
                <Select
                  value={getSetting('advanced.ai.optimization_strength') || 'medium'}
                  onChange={(e) => handleChange('advanced.ai.optimization_strength', e.target.value)}
                  label="优化强度"
                >
                  {optimizationStrengths.map((strength) => (
                    <MenuItem key={strength.value} value={strength.value}>
                      <Box>
                        <Typography variant="body1">{strength.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {strength.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Alert severity="info" sx={{ mt: 2 }}>
            这些参数将影响简历优化的效果，建议使用默认设置以获得最佳效果。
          </Alert>
        </CardContent>
      </Card>

      {/* 批量处理设置 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <BatchIcon sx={{ mr: 1, color: 'secondary.main' }} />
            批量处理设置
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="最大并发任务数"
                type="number"
                value={getSetting('advanced.batch.max_concurrent_jobs') || 3}
                onChange={(e) => handleChange('advanced.batch.max_concurrent_jobs', parseInt(e.target.value))}
                inputProps={{ min: 1, max: 10 }}
                size="small"
                helperText="同时处理的最大任务数量"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="批处理大小"
                type="number"
                value={getSetting('advanced.batch.batch_size') || 10}
                onChange={(e) => handleChange('advanced.batch.batch_size', parseInt(e.target.value))}
                inputProps={{ min: 1, max: 50 }}
                size="small"
                helperText="每批处理的项目数量"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('advanced.batch.auto_retry_failed') || true}
                    onChange={(e) => handleChange('advanced.batch.auto_retry_failed', e.target.checked)}
                  />
                }
                label="自动重试失败任务"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                失败的任务将自动重试
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="重试延迟 (毫秒)"
                type="number"
                value={getSetting('advanced.batch.retry_delay') || 5000}
                onChange={(e) => handleChange('advanced.batch.retry_delay', parseInt(e.target.value))}
                inputProps={{ min: 1000, max: 30000 }}
                size="small"
                helperText="重试前的等待时间"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 性能优化 */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <PerformanceIcon sx={{ mr: 1, color: 'secondary.main' }} />
            性能优化
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('advanced.performance.cache_enabled') || true}
                    onChange={(e) => handleChange('advanced.performance.cache_enabled', e.target.checked)}
                  />
                }
                label="启用缓存"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                缓存API响应以提高性能
              </Typography>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('advanced.performance.preload_data') || true}
                    onChange={(e) => handleChange('advanced.performance.preload_data', e.target.checked)}
                  />
                }
                label="预加载数据"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                预先加载常用数据
              </Typography>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('advanced.performance.lazy_loading') || true}
                    onChange={(e) => handleChange('advanced.performance.lazy_loading', e.target.checked)}
                  />
                }
                label="懒加载"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                按需加载内容
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Typography gutterBottom>缓存持续时间 (小时)</Typography>
              <Slider
                value={(getSetting('advanced.performance.cache_duration') || 3600000) / 3600000}
                onChange={(e, value) => handleChange('advanced.performance.cache_duration', value * 3600000)}
                min={0.5}
                max={24}
                step={0.5}
                marks={[
                  { value: 0.5, label: '30分钟' },
                  { value: 1, label: '1小时' },
                  { value: 6, label: '6小时' },
                  { value: 24, label: '24小时' }
                ]}
                valueLabelDisplay="on"
                valueLabelFormat={(value) => `${value}小时`}
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Typography variant="subtitle2" sx={{ mb: 2 }}>实验性功能</Typography>
          <Alert severity="warning" sx={{ mb: 2 }}>
            以下功能为实验性功能，可能不稳定，请谨慎启用。
          </Alert>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={false}
                    disabled
                  />
                }
                label="智能预测"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                基于历史数据预测最佳设置 (开发中)
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={false}
                    disabled
                  />
                }
                label="自动调优"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                自动调整参数以获得最佳效果 (开发中)
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}

export default AdvancedSettings;

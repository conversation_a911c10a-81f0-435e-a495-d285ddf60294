import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Close as CloseIcon
} from '@mui/icons-material';

const LoginSettings = ({ open, onClose }) => {
  const [loginForm, setLoginForm] = useState({
    email: '',
    password: '',
    headless: false,
    automation_type: 'selenium'
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [automationStatus, setAutomationStatus] = useState({});

  // API基础URL
  const API_BASE = 'http://localhost:8003/api/linkedin';

  // 获取自动化状态
  const fetchStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/status`);
      if (response.ok) {
        const data = await response.json();
        setAutomationStatus(data);
      }
    } catch (err) {
      console.error('获取状态失败:', err);
    }
  };

  // 设置自动化
  const setupAutomation = async () => {
    try {
      const response = await fetch(`${API_BASE}/setup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          automation_type: loginForm.automation_type,
          headless: loginForm.headless
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('设置自动化成功:', data);
        return true;
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || '设置自动化失败');
      }
    } catch (err) {
      console.error('设置自动化失败:', err);
      throw err;
    }
  };

  // 登录LinkedIn
  const loginLinkedIn = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // 先设置自动化
      await setupAutomation();

      // 然后登录
      const response = await fetch(`${API_BASE}/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: loginForm.email,
          password: loginForm.password,
          headless: loginForm.headless
        })
      });

      if (response.ok) {
        await response.json();
        setSuccess('LinkedIn登录成功！');
        // 开始监控登录状态
        const checkInterval = setInterval(async () => {
          await fetchStatus();
          if (automationStatus.is_logged_in) {
            clearInterval(checkInterval);
            setSuccess('LinkedIn登录验证完成！');
            setTimeout(() => {
              onClose();
            }, 2000);
          }
        }, 2000);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || '登录失败');
      }
    } catch (err) {
      console.error('登录失败:', err);
      setError('登录失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // 检查登录状态
  const _checkLoginStatus = async () => {
    setLoading(true);
    setError('');
    try {
      // 先检查是否有自动化实例
      const statusResponse = await fetch(`${API_BASE}/status`);
      if (statusResponse.ok) {
        const status = await statusResponse.json();
        
        // 如果没有自动化类型，先设置自动化
        if (!status.automation_type) {
          await setupAutomation();
        }
      }

      // 然后检查登录状态
      const response = await fetch(`${API_BASE}/verify-login`, {
        method: 'POST'
      });

      if (response.ok) {
        const data = await response.json();
        if (data.is_logged_in) {
          setSuccess('LinkedIn登录状态验证成功！');
        } else {
          setError('当前未登录LinkedIn，请使用登录功能进行登录');
        }
        await fetchStatus();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || '验证登录状态失败');
      }
    } catch (err) {
      console.error('检查登录状态失败:', err);
      setError('检查登录状态失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // 监听登录状态变化
  useEffect(() => {
    if (open) {
      fetchStatus();
      const statusInterval = setInterval(fetchStatus, 2000);
      return () => clearInterval(statusInterval);
    }
  }, [open]);

  // 监听登录状态变化，自动关闭对话框
  useEffect(() => {
    if (automationStatus.is_logged_in && open && success) {
      setTimeout(() => {
        onClose();
      }, 2000);
    }
  }, [automationStatus.is_logged_in, open, success, onClose]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        LinkedIn登录设置
        <IconButton
          onClick={onClose}
          sx={{ color: 'grey.500' }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ mb: 1 }}>
            <strong>智能登录检测：</strong>
          </Typography>
          <Typography variant="body2">
            • 如果您之前已登录LinkedIn，系统会自动检测并跳过登录步骤<br/>
            • 如果需要重新登录，请输入用户名密码进行登录<br/>
            • 登录过程中可能需要进行二次验证，请注意查看浏览器窗口
          </Typography>
        </Alert>

        {/* 显示当前任务状态 */}
        {automationStatus.current_task && (automationStatus.current_task.includes('验证') || automationStatus.current_task.includes('等待')) && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              {automationStatus.current_task}
            </Typography>
          </Alert>
        )}

        {/* 显示登录状态 */}
        {automationStatus.is_logged_in && (
          <Alert severity="success" sx={{ mb: 2 }}>
            <Typography variant="body2">
              ✅ 检测到已成功登录LinkedIn！
            </Typography>
          </Alert>
        )}

        {/* 显示成功信息 */}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {/* 显示错误信息 */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
          <TextField
            label="邮箱"
            value={loginForm.email}
            onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
            fullWidth
          />
          <TextField
            label="密码"
            type="password"
            value={loginForm.password}
            onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
            fullWidth
          />
          <FormControl fullWidth>
            <InputLabel id="automation-type-label">自动化类型</InputLabel>
            <Select
              labelId="automation-type-label"
              value={loginForm.automation_type}
              label="自动化类型"
              onChange={(e) => setLoginForm({ ...loginForm, automation_type: e.target.value })}
            >
              <MenuItem value="selenium">Selenium (稳定推荐)</MenuItem>
            </Select>
          </FormControl>
          <FormControlLabel
            control={
              <Switch
                checked={loginForm.headless}
                onChange={(e) => setLoginForm({ ...loginForm, headless: e.target.checked })}
              />
            }
            label="无头模式"
          />
          <Typography variant="body2" color="text.secondary">
            注意：建议关闭无头模式，以便处理可能出现的验证码或二次验证。
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 3, gap: 1 }}>
        {/* 验证登录状态按钮已隐藏，因为与运行状态中的验证登录功能重复 */}
        {/* <Button 
          onClick={checkLoginStatus} 
          variant="outlined" 
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <span style={{fontSize: '1.2em'}}>✨</span>}
        >
          {loading ? '检查中...' : '验证登录状态'}
        </Button> */}
        <Button 
          onClick={loginLinkedIn} 
          variant="contained" 
          disabled={loading || automationStatus.is_logged_in}
          startIcon={loading ? <CircularProgress size={20} /> : <SettingsIcon />}
        >
          {automationStatus.is_logged_in ? '已登录' : (loading ? '登录中...' : '登录')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default LoginSettings;
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemButton, // 导入ListItemButton
  ListItemText,
  Paper,
  CircularProgress,
  Chip,
  Container, // 引入Container
  Divider, // 导入Divider
  Alert, // 导入Alert
} from '@mui/material';
import {
  Description as DescriptionIcon,
  Code as CodeIcon,
  Build as BuildIcon,
  Settings as SettingsIcon,
  BugReport as BugReportIcon,
  Assessment as AssessmentIcon,
  MenuBook as MenuBookIcon
} from '@mui/icons-material';

const GuidePage = () => {
  const [selectedDoc, setSelectedDoc] = useState('overview');
  const [docContent, setDocContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 文档分类和结构
  const docCategories = [
    {
      title: '快速入门',
      icon: <MenuBookIcon />,
      items: [
        { id: 'overview', title: '项目概述', file: 'README.md', description: '项目介绍和主要功能' },
        { id: 'installation', title: '安装指南', file: 'README.md', description: '环境配置和依赖安装' },
        { id: 'quickstart', title: '快速开始', file: 'README.md', description: '快速上手使用教程' }
      ]
    },
    {
      title: '技术增强',
      icon: <CodeIcon />,
      items: [
        { id: 'mcp-guide', title: 'MCP增强功能', file: 'MCP_ENHANCEMENT_GUIDE.md', description: 'Model Context Protocol增强指南' },
        { id: 'linkedin-enhancements', title: 'LinkedIn功能增强', file: 'LINKEDIN_ENHANCEMENTS_SUMMARY.md', description: 'LinkedIn自动化功能优化' },
        { id: 'optimization', title: '系统优化总结', file: 'OPTIMIZATION_SUMMARY.md', description: '性能优化和改进措施' }
      ]
    },
    {
      title: '配置指南',
      icon: <SettingsIcon />,
      items: [
        { id: 'chromedriver', title: 'ChromeDriver配置', file: 'UNDETECTED_CHROMEDRIVER_GUIDE.md', description: '反检测浏览器驱动配置' },
        { id: 'loading-detection', title: '加载模式检测', file: 'LOADING_MODE_DETECTION.md', description: '页面加载状态检测机制' }
      ]
    },
    {
      title: '问题修复',
      icon: <BugReportIcon />,
      items: [
        { id: 'pagination-deadloop', title: '分页死循环修复', file: 'PAGINATION_DEADLOOP_FIX.md', description: '解决分页无限循环问题' },
        { id: 'pagination-flip', title: '分页翻转修复', file: 'PAGINATION_FLIP_FIX.md', description: '修复分页方向错误' },
        { id: 'scroll-position', title: '滚动位置修复', file: 'SCROLL_POSITION_FIX_COMPLETE.md', description: '页面滚动定位优化' },
        { id: 'multi-page', title: '多页分页完善', file: 'MULTI_PAGE_PAGINATION_COMPLETE.md', description: '多页面分页机制改进' }
      ]
    },
    {
      title: '测试报告',
      icon: <AssessmentIcon />,
      items: [
        { id: 'pagination-analysis', title: '分页分析报告', file: 'linkedin_pagination_analysis_report.md', description: 'LinkedIn分页机制分析' },
        { id: 'multi-page-test', title: '多页支持测试', file: 'multi_page_support_test_report.md', description: '多页面功能测试结果' },
        { id: 'pagination-test', title: '分页修复测试', file: 'pagination_fix_test_report.md', description: '分页功能修复验证' }
      ]
    },
    {
      title: '开发贡献',
      icon: <BuildIcon />,
      items: [
        { id: 'contributing', title: '贡献指南', file: 'CONTRIBUTING.md', description: '项目贡献规范和流程' },
        { id: 'import-fixes', title: '导入修复总结', file: 'IMPORT_FIXES_SUMMARY.md', description: '模块导入问题解决方案' }
      ]
    }
  ];

  // 加载文档内容
  const loadDocContent = async (filename) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`http://localhost:8003/api/docs/${filename}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const content = await response.text();
      setDocContent(content);
    } catch (err) {
      console.error('Error loading document:', err);
      setError('文档加载失败，请稍后重试');
      // 设置默认内容
      setDocContent(getDefaultContent(selectedDoc));
    } finally {
      setLoading(false);
    }
  };

  // 获取默认内容
  const getDefaultContent = (docId) => {
    const defaultContents = {
      'overview': `# 🎯 Jobs Applier AI Agent - 项目概述

## 简介

Jobs Applier AI Agent (AIHawk) 是一个基于人工智能的智能求职助手平台，集成了简历优化、求职信生成和LinkedIn自动化等功能。**项目已达到完美运行状态，所有核心功能模块都运行稳定。**

## 🚀 核心功能

### 🎨 智能简历优化 (完美状态)
- **AI驱动定制化**：基于Google Gemini 2.5 Flash Preview，智能分析职位要求并优化简历内容
- **PDF上传支持**：上传现有简历进行AI分析和针对性优化
- **专业模板**：多种优雅的简历模板适用于不同行业
- **实时预览**：HTML预览配合高质量PDF导出
- **多语言支持**：完美支持中英文简历
- **内容高亮**：优化内容用✨图标标记，可选择隐藏高亮

### 💌 智能求职信生成 (完美状态)
- **个性化内容**：基于公司信息和职位要求生成500+字定制化求职信
- **公司Logo集成**：自动获取目标公司Logo提升专业度
- **双语支持**：支持中英文求职信生成
- **匹配度分析**：可视化图表展示候选人-职位匹配度
- **优雅模板**：专业设计的求职信模板，支持HTML/PDF导出

### 🤖 LinkedIn自动化 (完美状态)
- **智能职位搜索**：基于关键词、地点等条件精准搜索
- **批量Easy Apply申请**：支持大规模自动化申请，优化后90-120秒/申请
- **智能问答处理**：自动处理LinkedIn申请过程中的各种问题类型
- **申请状态跟踪**：实时显示"申请"、"申请中"、"已申请"状态
- **职位发布时间**：提取并显示职位发布时间，支持时间排序
- **成功通知自动关闭**：自动检测并关闭申请成功通知窗口
- **Chrome清理功能**：安全清理自动化数据，解决连接问题

### 🌐 现代化Web界面 (完美状态)
- **响应式设计**：支持桌面和移动设备
- **实时日志显示**：WebSocket实时日志流，详细的申请进度反馈
- **会话数据持久化**：浏览器会话内数据保持
- **优雅的UI组件**：Material-UI组件库，Apple风格液态玻璃效果

## 🛠️ 技术架构

- **前端**：React 18 + Vite + Material-UI
- **后端**：Python 3.8+ + FastAPI
- **AI引擎**：Google Gemini 2.5 Flash Preview (推荐)
- **自动化**：Selenium + Undetected Chrome (推荐稳定)
- **数据存储**：YAML配置文件
- **实时通信**：WebSocket日志流

## 📦 快速开始

1. **克隆项目仓库**
2. **安装Python和Node.js依赖**
3. **配置Gemini API密钥** (推荐，性能更好成本更低)
4. **启动后端服务** (localhost:8003)
5. **启动前端服务** (localhost:3000)
6. **访问Web界面开始使用**

## 🎯 项目状态

**当前状态**: 🎉 **完美运行状态**
- ✅ LinkedIn自动化系统完全稳定
- ✅ AI简历优化功能完善
- ✅ 求职信生成系统完整
- ✅ Web界面响应流畅
- ✅ 所有已知问题已解决

详细安装步骤请参考安装指南。`,
      'installation': `# 📋 安装指南

## 系统要求

- **Python 3.8+** (推荐3.10+)
- **Node.js 16+** (推荐18+)
- **Chrome浏览器** (最新版本)
- **8GB+ RAM推荐** (包含Chrome自动化)
- **2GB+ 磁盘空间** (包含依赖和缓存)

## 快速安装 (推荐)

### 1. 克隆项目
\`\`\`bash
git clone https://github.com/UJJacky/Jobs-Application_Linkedin_AIHawk.git
cd Jobs-Application_Linkedin_AIHawk
\`\`\`

### 2. 创建Python虚拟环境
\`\`\`bash
# 创建虚拟环境
python -m venv virtual

# 激活虚拟环境
virtual\\Scripts\\activate  # Windows
# source virtual/bin/activate  # Linux/Mac
\`\`\`

### 3. 安装Python依赖
\`\`\`bash
# 安装核心依赖
pip install -r requirements.txt

# 可选：安装LinkedIn特定依赖
pip install -r requirements_linkedin.txt
\`\`\`

### 4. 安装前端依赖
\`\`\`bash
cd webui/frontend
npm install
cd ../..
\`\`\`

### 5. 配置API密钥
\`\`\`bash
# 复制配置模板
cp config.py.example config.py
cp data_folder/secrets.yaml.example data_folder/secrets.yaml

# 编辑 data_folder/secrets.yaml
\`\`\`

**secrets.yaml 配置示例**：
\`\`\`yaml
# Gemini API (推荐 - 性能更好，成本更低)
gemini_api_key: "your-gemini-api-key-here"

# OpenAI API (备选)
openai_api_key: "your-openai-api-key-here"

# LinkedIn账户 (可选，也可在界面中输入)
linkedin_email: "<EMAIL>"
linkedin_password: "your-password"
\`\`\`

### 6. 启动服务

**启动后端服务**：
\`\`\`bash
cd webui/backend
python main.py
\`\`\`
✅ 后端运行在: **http://localhost:8003**

**启动前端服务** (新终端窗口)：
\`\`\`bash
cd webui/frontend
npm start
\`\`\`
✅ 前端运行在: **http://localhost:3000**

### 7. 访问应用
打开浏览器访问: **http://localhost:3000**

## 高级配置

### AI模型配置 (config.py)
\`\`\`python
# AI模型设置
LLM_MODEL = 'gemini-2.5-flash-preview-0514'  # 推荐
LLM_TEMPERATURE = 1.0
LLM_TIMEOUT = 60
LLM_MAX_RETRIES = 3
\`\`\`

### LinkedIn自动化配置 (linkedin_config.yaml)
\`\`\`yaml
selenium:
  headless: false  # 推荐关闭，便于处理验证
  timeout: 30
  window_size: [1200, 800]

automation:
  max_applications_per_day: 50
  application_delay: [30, 60]  # 秒
\`\`\`

## 故障排除

### Python依赖问题
\`\`\`bash
# 升级pip和setuptools
pip install --upgrade pip setuptools wheel

# 强制重新安装
pip install --force-reinstall package-name
\`\`\`

### Node.js依赖问题
\`\`\`bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
\`\`\`

### Chrome自动化问题
\`\`\`bash
# 检查Chrome版本
google-chrome --version

# 更新webdriver-manager
pip install --upgrade webdriver-manager
\`\`\`

## 验证安装

### 1. 检查后端服务
访问: http://localhost:8003/docs (FastAPI文档)

### 2. 检查前端服务
访问: http://localhost:3000 (React应用)

### 3. 测试LinkedIn自动化
1. 进入LinkedIn自动化页面
2. 点击"设置自动化"
3. 验证Chrome浏览器正常启动

## 生产环境部署

### 环境变量设置
\`\`\`bash
# 生产环境
export NODE_ENV=production
export LOG_LEVEL=INFO

# 安全设置
export SECURE_COOKIES=true
export HTTPS_ONLY=true
\`\`\`

### Docker部署 (可选)
\`\`\`bash
# 构建镜像
docker build -t aihawk .

# 运行容器
docker run -p 3000:3000 -p 8003:8003 aihawk
\`\`\`

安装完成后，请参考快速开始指南进行首次配置。`,
      'quickstart': `# 🚀 快速开始

## 第一次使用

### 1. 访问Web界面
打开浏览器访问 **http://localhost:3000**

### 2. 配置API密钥
- 进入设置页面
- 配置**Gemini API密钥** (推荐，性能更好成本更低)
- 或配置OpenAI API密钥作为备选

### 3. LinkedIn自动化设置
- 进入"LinkedIn自动化"页面
- 点击**"设置自动化"**按钮 (选择Selenium模式，最稳定)
- 在弹出的Chrome浏览器中**手动登录LinkedIn**
- 点击**"验证登录状态"**确认登录成功

### 4. 开始职位搜索和申请
- 设置搜索关键词和地点
- 点击**"搜索职位"**
- 查看搜索结果，包含职位发布时间
- 选择职位进行单个或批量申请
- 实时监控申请进度和状态

## 🎯 核心功能使用

### 🤖 LinkedIn自动化 (推荐功能)
1. **设置搜索条件**：关键词、地点、经验等
2. **智能职位搜索**：自动滚动加载，确保完整职位列表
3. **批量Easy Apply申请**：支持大规模自动化申请
4. **实时状态跟踪**：申请按钮显示"申请"→"申请中"→"已申请"
5. **申请历史记录**：详细的时间戳和成功率统计

### 🎨 简历优化
1. **上传现有简历PDF**或从头创建
2. **输入目标职位URL**进行AI分析
3. **AI自动优化内容**，用✨图标标记优化部分
4. **实时预览效果**，支持隐藏/显示高亮
5. **导出高质量PDF**

### 💌 求职信生成
1. **输入目标公司和职位信息**
2. **AI生成500+字个性化求职信**
3. **自动获取公司Logo**提升专业度
4. **查看匹配度分析图表**
5. **下载HTML/PDF格式**

## ⚡ 最新优化功能

### LinkedIn申请流程优化
- **申请速度提升**：优化后平均90-120秒/申请 (减少5-10秒)
- **智能等待策略**：使用WebDriverWait替代固定延迟
- **自动成功通知关闭**：无需手动关闭申请成功弹窗
- **职位发布时间显示**：支持按时间排序筛选

### Chrome清理功能
- **一键清理**：运行状态卡片中的"🧹 清理Chrome数据"按钮
- **安全清理**：只清理自动化数据，不影响个人Chrome
- **解决连接问题**：修复Undetected Chrome连接失败

### UI/UX改进
- **实时日志显示**：详细的申请进度反馈
- **按钮状态优化**：文本不换行，状态清晰
- **响应式布局**：完美支持桌面和移动设备

## 💡 使用建议

### 最佳实践
1. **首次使用先测试**：建议先申请5-10个职位测试效果
2. **定期清理Chrome**：每周执行一次Chrome数据清理
3. **监控申请成功率**：根据反馈调整搜索条件
4. **合理设置申请频率**：避免过于频繁申请

### 注意事项
- **网络稳定性**：确保稳定的网络连接
- **LinkedIn账户活跃**：定期手动使用LinkedIn保持活跃
- **API配额管理**：合理使用Gemini API配额
- **Chrome版本**：保持Chrome浏览器更新

## 🔧 故障排除

### 常见问题
- **Undetected Chrome连接失败**：使用Chrome清理功能
- **申请按钮无响应**：检查登录状态，重新验证登录
- **职位加载不完整**：系统已优化三阶段滚动策略
- **申请速度慢**：系统已优化，现在平均90-120秒/申请

### 获取帮助
- 查看实时日志显示了解详细进度
- 检查运行状态卡片中的系统状态
- 使用Chrome清理功能解决大部分连接问题`
    };
    return defaultContents[docId] || '# 文档内容加载中...';
  };

  // 渲染Markdown内容
  const renderMarkdown = (content) => {
    const lines = content.split('\n');
    const elements = [];
    let inCodeBlock = false;
    let codeBlockContent = [];
    let codeBlockLanguage = '';
    let inList = false;
    let listItems = [];

    const flushList = () => {
      if (listItems.length > 0) {
        elements.push(
          <Box 
            key={`list-${elements.length}`} 
            component="ul" 
            sx={{ 
              pl: 3, 
              my: 2, 
              '& li': {
                mb: 1,
                lineHeight: 1.6
              }
            }}
          >
            {listItems.map((item, idx) => (
              <Typography key={idx} component="li" sx={{ mb: 1 }}>
                {item}
              </Typography>
            ))}
          </Box>
        );
        listItems = [];
        inList = false;
      }
    };

    const flushCodeBlock = () => {
      if (codeBlockContent.length > 0) {
        elements.push(
          <Box 
            key={`code-${elements.length}`} 
            sx={{ 
              bgcolor: '#10151a', 
              color: '#f1f1f1',
              p: 2, 
              borderRadius: '12px', 
              my: 3, 
              overflow: 'auto',
              fontFamily: 'Consolas, Monaco, "Courier New", monospace',
              fontSize: '0.9rem',
              border: '1px solid',
              borderColor: 'rgba(100, 100, 120, 0.3)',
              maxWidth: '100%'
            }}
          >
            {codeBlockLanguage && (
              <Typography 
                variant="caption" 
                sx={{ 
                  color: 'grey.400', 
                  display: 'block', 
                  mb: 1,
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}
              >
                {codeBlockLanguage}
              </Typography>
            )}
            <Box 
              component="pre" 
              sx={{ 
                m: 0, 
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                overflowX: 'auto'
              }}
            >
              <code>{codeBlockContent.join('\n')}</code>
            </Box>
          </Box>
        );
        codeBlockContent = [];
        codeBlockLanguage = '';
        inCodeBlock = false;
      }
    };

    lines.forEach((line, index) => {
      // 处理代码块
      if (line.startsWith('```')) {
        if (inCodeBlock) {
          flushCodeBlock();
        } else {
          flushList();
          inCodeBlock = true;
          codeBlockLanguage = line.substring(3).trim();
        }
        return;
      }

      if (inCodeBlock) {
        codeBlockContent.push(line);
        return;
      }

      // 处理列表项
      if (line.startsWith('- ') || line.match(/^\d+\. /)) {
        if (!inList) {
          inList = true;
        }
        listItems.push(line.replace(/^[-\d+.] /, ''));
        return;
      } else if (inList && line.trim() !== '') {
        // 继续列表项（缩进内容）
        if (line.startsWith('  ')) {
          listItems[listItems.length - 1] += '\n' + line.trim();
          return;
        } else {
          flushList();
        }
      } else if (inList) {
        flushList();
      }

      // 处理标题
      if (line.startsWith('# ')) {
        flushList();
        elements.push(
          <Typography 
            key={index} 
            variant="h4" 
            component="h1" 
            sx={{ 
              mt: 4, 
              mb: 3, 
              fontWeight: 'bold',
              color: 'primary.main',
              borderBottom: '3px solid',
              borderColor: 'primary.main',
              pb: 1.5,
              fontSize: '1.75rem'
            }}
          >
            {line.substring(2)}
          </Typography>
        );
      } else if (line.startsWith('## ')) {
        flushList();
        elements.push(
          <Typography 
            key={index} 
            variant="h5" 
            component="h2" 
            sx={{ 
              mt: 3.5, 
              mb: 2, 
              fontWeight: 'bold',
              color: 'text.primary',
              fontSize: '1.5rem',
              borderLeft: '4px solid',
              borderColor: 'primary.main',
              pl: 2
            }}
          >
            {line.substring(3)}
          </Typography>
        );
      } else if (line.startsWith('### ')) {
        flushList();
        elements.push(
          <Typography 
            key={index} 
            variant="h6" 
            component="h3" 
            sx={{ 
              mt: 3, 
              mb: 1.5, 
              fontWeight: 'bold',
              color: 'text.primary',
              fontSize: '1.25rem'
            }}
          >
            {line.substring(4)}
          </Typography>
        );
      } else if (line.trim() === '') {
        elements.push(<Box key={index} sx={{ height: 12 }} />);
      } else if (line.trim()) {
        // 处理内联代码
        const processInlineCode = (text) => {
          const parts = text.split(/(`[^`]+`)/);
          return parts.map((part, idx) => {
            if (part.startsWith('`') && part.endsWith('`')) {
              return (
                <Box 
                  key={idx}
                  component="code" 
                  sx={{ 
                    bgcolor: 'grey.100', 
                    color: 'error.main',
                    px: 0.5, 
                    py: 0.25,
                    borderRadius: 0.5,
                    fontFamily: 'monospace',
                    fontSize: '0.875em'
                  }}
                >
                  {part.slice(1, -1)}
                </Box>
              );
            }
            return part;
          });
        };

        elements.push(
          <Typography 
            key={index} 
            paragraph 
            sx={{ 
              mb: 2,
              lineHeight: 1.8,
              fontSize: '1rem',
              color: 'text.primary'
            }}
          >
            {processInlineCode(line)}
          </Typography>
        );
      }
    });

    // 清理剩余内容
    flushList();
    flushCodeBlock();

    return elements;
  };

  // 初始加载
  useEffect(() => {
    const currentItem = docCategories.flatMap(cat => cat.items).find(item => item.id === selectedDoc);
    if (currentItem) {
      loadDocContent(currentItem.file);
    }
  }, [selectedDoc]);

  return (
    <Container maxWidth="xl" sx={{ pt: 4, pb: 4 }}>
      <Box sx={{
        display: 'flex',
        gap: 3,
        alignItems: 'flex-start',
        position: 'relative',
        zIndex: 1,
        ml: 'calc(10cm - 2cm)', // 整体左移2cm
      }}>
        {/* 左侧导航栏 */}
        <Paper
          elevation={4}
          sx={{
            minWidth: '320px',
            width: '25%',
            maxWidth: '360px',
            height: 'calc(100vh - 120px)',
            maxHeight: '800px',
            overflowY: 'auto',
            position: 'sticky',
            top: '80px',
            bgcolor: '#10151a',
            borderRadius: '16px',
            zIndex: 1001,
            p: 2.5,
            transition: 'box-shadow 0.35s cubic-bezier(0.4,0,0.2,1), transform 0.35s cubic-bezier(0.4,0,0.2,1)',
            boxShadow: '0 0 0 rgba(0,212,255,0)',
            '&:hover': {
              boxShadow: '0 0 24px 0 rgba(0,212,255,0.25), 0 0 2px 0 rgba(0,212,255,0.18)',
              transform: 'translateY(-2px) scale(1.012)'
            },
            '&::-webkit-scrollbar': {
              width: '8px',
            },
          }}
          >
            <Box sx={{ p: 2, borderBottom: '1.5px solid', borderColor: '#00eaff' }}>
              <Typography variant="h5" sx={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 1, color: '#00eaff', letterSpacing: 1 }}>
                <DescriptionIcon sx={{ color: '#00eaff' }} />
                使用指南
              </Typography>
              <Typography variant="body1" sx={{ mt: 0.5, color: '#00eaff', opacity: 0.8 }}>
                项目文档和技术指南
              </Typography>
            </Box>
            {docCategories.map((category, categoryIndex) => (
              <Box key={categoryIndex}>
                <Box sx={{ p: 2, bgcolor: 'grey.900' }}>
                  <Typography 
                    variant="subtitle1" 
                    sx={{ fontWeight: 'bold', color: '#00eaff', display: 'flex', alignItems: 'center', gap: 1, letterSpacing: 1 }}>
                    {category.icon}
                    {category.title}
                  </Typography>
                </Box>
                <List dense>
                  {category.items.map((item) => (
                    <ListItem key={item.id} disablePadding>
                      <ListItemButton
                        selected={selectedDoc === item.id}
                        onClick={() => setSelectedDoc(item.id)}
                        sx={{
                          pl: 3,
                          borderRadius: '8px',
                          mb: 1,
                          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                          color: selectedDoc === item.id ? '#ffffff' : '#e0e0e0',
                          backgroundColor: selectedDoc === item.id ? 'rgba(0, 212, 255, 0.12)' : 'transparent',
                          fontWeight: selectedDoc === item.id ? 600 : 500,
                          '&:hover': {
                            color: '#ffffff',
                            backgroundColor: 'rgba(255, 255, 255, 0.05)',
                            '&::after': {
                              content: '""',
                              position: 'absolute',
                              bottom: 0,
                              left: '50%',
                              transform: 'translateX(-50%)',
                              width: '60%',
                              height: '2px',
                              backgroundColor: '#00d4ff',
                              borderRadius: '1px',
                              opacity: 0.8
                            }
                          }
                        }}
                      >
                        <ListItemText
                          primary={item.title}
                          secondary={item.description}
                          primaryTypographyProps={{ fontSize: '1.1rem', fontWeight: selectedDoc === item.id ? 'bold' : 'normal' }}
                          secondaryTypographyProps={{ fontSize: '0.9rem' }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
                {categoryIndex < docCategories.length - 1 && <Divider />}
              </Box>
            ))}
          </Paper>
          {/* 右侧内容区域 */}
          <Paper
            elevation={0}
            sx={{
              flex: 1,
              minWidth: 0,
              maxWidth: 'calc(100vw - 480px - 8.5cm)',
              height: 'calc(100vh - 120px)',
              maxHeight: '800px',
              overflowY: 'auto',
              bgcolor: 'transparent',
              borderRadius: '16px',
              zIndex: 1000,
              p: 0,
              transition: 'box-shadow 0.35s cubic-bezier(0.4,0,0.2,1), transform 0.35s cubic-bezier(0.4,0,0.2,1)',
              boxShadow: '0 0 0 rgba(0,212,255,0)',
              '&:hover': {
                boxShadow: '0 0 24px 0 rgba(0,212,255,0.25), 0 0 2px 0 rgba(0,212,255,0.18)',
                transform: 'translateY(-2px) scale(1.012)'
              },
              '&::-webkit-scrollbar': {
                width: '8px'
              }
            }}>
              {(() => {
                if (loading) {
                  return (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <CircularProgress color="info" />
                    </Box>
                  );
                }
                if (error) {
                  return (
                    <Alert severity="error" sx={{ m: 3 }}>
                      {error}
                    </Alert>
                  );
                }
                const currentDoc = docCategories.flatMap(cat => cat.items).find(item => item.id === selectedDoc);
                return (
                  <>
                    {/* 内容头部 */}
                    <Box
                      sx={{
                        p: 3,
                        bgcolor: '#10151a',
                        borderBottom: '2px solid',
                        borderColor: '#00eaff',
                        borderRadius: '16px 16px 0 0',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                        justifyContent: 'space-between',
                        minHeight: 64,
                        position: 'sticky',
                        top: 0,
                        zIndex: 10,
                      }}
                    >
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', flex: 1 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#fff', mb: 0.5, letterSpacing: 1, fontSize: '2rem', lineHeight: 1.1 }}>
                          {currentDoc?.title}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#fff', opacity: 0.8, fontSize: '1rem', mt: 0.5 }}>
                          {currentDoc?.description}
                        </Typography>
                      </Box>
                      <Chip
                        label={currentDoc?.file}
                        size="small"
                        variant="outlined"
                        sx={{ color: '#00eaff', borderColor: '#00eaff', fontWeight: 700, fontSize: 15, px: 1.5, background: '#181a1b', height: 32 }}
                      />
                    </Box>
                    {/* 文档内容 */}
                    <Box sx={{ p: 4, flex: 1, overflowY: 'auto', bgcolor: 'grey.900', fontSize: 15, color: '#fff', lineHeight: 1.85, borderRadius: '0 0 16px 16px', paddingTop: 2 }}>
                      {renderMarkdown(docContent)}
                    </Box>
                  </>
                );
              })()}
            </Paper>
          </Box>
        </Container>
      );
    };

    export default GuidePage;
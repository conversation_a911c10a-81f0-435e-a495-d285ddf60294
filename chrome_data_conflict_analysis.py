#!/usr/bin/env python3
"""
Chrome浏览器数据冲突分析
分析Undetected Chrome与标准Chrome浏览器数据的关系和清理需求
"""

import sys
import os
import shutil
from pathlib import Path
from src.logger_config import logger

def analyze_chrome_data_separation():
    """分析Chrome数据分离情况"""
    
    print("🔍 Chrome浏览器数据冲突分析")
    print("="*80)
    
    # 1. 配置目录分析
    print("\n1️⃣ 配置目录分离分析")
    print("-"*50)
    
    directories = {
        "用户标准Chrome": {
            "Windows": r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data",
            "说明": "用户日常使用的Chrome浏览器数据",
            "包含内容": ["书签", "密码", "历史记录", "扩展程序", "Cookie", "缓存"]
        },
        "Undetected Chrome": {
            "路径": str(Path.home() / ".linkedin_automation" / "chrome_profile"),
            "说明": "LinkedIn自动化专用的独立配置目录",
            "包含内容": ["LinkedIn登录状态", "自动化相关设置", "独立Cookie", "独立缓存"]
        },
        "标准Selenium": {
            "路径": str(Path.home() / ".linkedin_automation" / "chrome_profile_selenium"),
            "说明": "标准Selenium专用的独立配置目录",
            "包含内容": ["备用LinkedIn登录状态", "Selenium相关设置", "独立Cookie", "独立缓存"]
        }
    }
    
    for name, info in directories.items():
        print(f"\n📁 {name}:")
        for key, value in info.items():
            if isinstance(value, list):
                print(f"  {key}: {', '.join(value)}")
            else:
                print(f"  {key}: {value}")
    
    return directories

def analyze_data_isolation():
    """分析数据隔离情况"""
    
    print(f"\n2️⃣ 数据隔离分析")
    print("-"*50)
    
    isolation_analysis = {
        "完全隔离": {
            "状态": "✅ 是",
            "说明": "系统使用完全独立的配置目录",
            "证据": [
                "Undetected Chrome: ~/.linkedin_automation/chrome_profile",
                "标准Selenium: ~/.linkedin_automation/chrome_profile_selenium", 
                "用户Chrome: 系统默认位置（完全不同）"
            ],
            "结论": "三套Chrome配置完全独立，互不影响"
        },
        "Cookie隔离": {
            "状态": "✅ 完全隔离",
            "说明": "每个配置目录有独立的Cookie存储",
            "影响": "LinkedIn登录状态在三个环境中独立维护"
        },
        "缓存隔离": {
            "状态": "✅ 完全隔离", 
            "说明": "每个配置目录有独立的缓存空间",
            "影响": "不会相互污染缓存数据"
        },
        "进程隔离": {
            "状态": "✅ 基本隔离",
            "说明": "使用不同的用户数据目录启动独立Chrome进程",
            "注意": "但可能共享某些系统级Chrome组件"
        }
    }
    
    for aspect, details in isolation_analysis.items():
        print(f"\n🔒 {aspect}:")
        for key, value in details.items():
            if isinstance(value, list):
                print(f"  {key}:")
                for item in value:
                    print(f"    - {item}")
            else:
                print(f"  {key}: {value}")
    
    return isolation_analysis

def analyze_instability_root_causes():
    """分析不稳定性根本原因"""
    
    print(f"\n3️⃣ Undetected Chrome不稳定性根本原因分析")
    print("-"*50)
    
    root_causes = {
        "主要原因": {
            "Chrome进程管理": {
                "问题": "Chrome进程启动/关闭时的竞态条件",
                "表现": "session not created, cannot connect to chrome",
                "与用户Chrome关系": "❌ 无关 - 使用独立配置目录"
            },
            "调试端口冲突": {
                "问题": "--remote-debugging-port端口占用",
                "表现": "端口绑定失败，连接超时",
                "与用户Chrome关系": "❌ 无关 - 使用不同端口"
            },
            "ChromeDriver版本兼容": {
                "问题": "Chrome版本与ChromeDriver版本不匹配",
                "表现": "版本兼容性错误",
                "与用户Chrome关系": "⚠️ 间接相关 - 共享Chrome二进制文件"
            },
            "配置文件损坏": {
                "问题": "Preferences文件JSON格式错误",
                "表现": "expecting ',' delimiter错误",
                "与用户Chrome关系": "❌ 无关 - 独立配置文件"
            }
        },
        "次要原因": {
            "系统资源竞争": {
                "问题": "内存/CPU资源不足",
                "表现": "启动缓慢，响应超时",
                "与用户Chrome关系": "🟡 可能相关 - 共享系统资源"
            },
            "Windows权限问题": {
                "问题": "配置目录权限不足",
                "表现": "文件创建/写入失败",
                "与用户Chrome关系": "❌ 无关 - 独立目录权限"
            }
        }
    }
    
    for category, causes in root_causes.items():
        print(f"\n🔍 {category}:")
        for cause_name, details in causes.items():
            print(f"  📋 {cause_name}:")
            for key, value in details.items():
                print(f"    {key}: {value}")
    
    return root_causes

def analyze_cleanup_necessity():
    """分析清理必要性"""
    
    print(f"\n4️⃣ 清理用户Chrome数据的必要性分析")
    print("-"*50)
    
    cleanup_analysis = {
        "清理用户Chrome数据": {
            "必要性": "❌ 不必要",
            "原因": [
                "系统使用完全独立的配置目录",
                "用户Chrome数据与自动化Chrome完全隔离",
                "清理用户数据会影响用户正常使用",
                "不会解决Undetected Chrome的连接问题"
            ],
            "风险": [
                "🔴 用户丢失所有浏览器数据（书签、密码等）",
                "🔴 用户需要重新登录所有网站",
                "🔴 用户体验严重受损",
                "🔴 可能引起用户投诉"
            ]
        },
        "清理自动化Chrome数据": {
            "必要性": "🟡 有时必要",
            "适用场景": [
                "配置文件损坏（JSON格式错误）",
                "LinkedIn登录状态异常",
                "缓存数据过大影响性能",
                "需要重置自动化环境"
            ],
            "清理目标": [
                "~/.linkedin_automation/chrome_profile/",
                "~/.linkedin_automation/chrome_profile_selenium/",
                "ChromeDriver缓存文件",
                "临时文件和日志"
            ],
            "风险": "🟢 低风险 - 只影响自动化功能"
        }
    }
    
    for cleanup_type, details in cleanup_analysis.items():
        print(f"\n🧹 {cleanup_type}:")
        for key, value in details.items():
            if isinstance(value, list):
                print(f"  {key}:")
                for item in value:
                    print(f"    {item}")
            else:
                print(f"  {key}: {value}")
    
    return cleanup_analysis

def provide_stability_recommendations():
    """提供稳定性建议"""
    
    print(f"\n5️⃣ 稳定性改善建议")
    print("-"*50)
    
    recommendations = {
        "立即可行的改善措施": {
            "1. 定期清理自动化配置": {
                "操作": "清理 ~/.linkedin_automation/ 目录",
                "频率": "每周或出现问题时",
                "命令": "rm -rf ~/.linkedin_automation/chrome_profile*",
                "效果": "解决配置文件损坏问题"
            },
            "2. 优化Chrome进程管理": {
                "操作": "增强进程清理逻辑",
                "已实现": "✅ force_cleanup_all()方法",
                "效果": "减少进程冲突"
            },
            "3. 端口管理优化": {
                "操作": "动态端口分配",
                "已实现": "✅ _cleanup_debug_ports()方法", 
                "效果": "避免端口冲突"
            }
        },
        "中期改善措施": {
            "1. Chrome版本锁定": {
                "操作": "使用固定Chrome版本",
                "实现": "指定version_main参数",
                "效果": "避免版本兼容问题"
            },
            "2. 健康检查机制": {
                "操作": "定期检查Chrome连接状态",
                "实现": "添加心跳检测",
                "效果": "提前发现连接问题"
            }
        },
        "长期解决方案": {
            "1. 迁移到Playwright": {
                "优势": "更稳定的浏览器自动化",
                "状态": "✅ 已实现同步模式",
                "建议": "逐步迁移核心功能"
            },
            "2. 混合模式优化": {
                "策略": "智能选择最稳定的驱动",
                "状态": "✅ 已实现基础版本",
                "建议": "增加更多智能判断逻辑"
            }
        }
    }
    
    for category, measures in recommendations.items():
        print(f"\n💡 {category}:")
        for measure_name, details in measures.items():
            print(f"  {measure_name}:")
            for key, value in details.items():
                print(f"    {key}: {value}")
    
    return recommendations

def create_safe_cleanup_script():
    """创建安全的清理脚本"""
    
    print(f"\n6️⃣ 安全清理脚本")
    print("-"*50)
    
    script_content = '''#!/usr/bin/env python3
"""
LinkedIn自动化Chrome数据安全清理脚本
⚠️ 只清理自动化相关数据，不影响用户Chrome浏览器
"""

import shutil
from pathlib import Path
from src.logger_config import logger

def safe_cleanup_automation_data():
    """安全清理自动化数据"""
    
    logger.info("🧹 开始安全清理LinkedIn自动化Chrome数据...")
    
    # 要清理的目录列表（只包含自动化相关）
    cleanup_targets = [
        Path.home() / ".linkedin_automation" / "chrome_profile",
        Path.home() / ".linkedin_automation" / "chrome_profile_selenium",
        Path.home() / ".wdm" / "drivers" / "chromedriver",  # ChromeDriver缓存
        Path.home() / "appdata" / "roaming" / "undetected_chromedriver"  # UC缓存
    ]
    
    cleaned_count = 0
    
    for target in cleanup_targets:
        if target.exists():
            try:
                if target.is_dir():
                    shutil.rmtree(target)
                    logger.info(f"✅ 已清理目录: {target}")
                else:
                    target.unlink()
                    logger.info(f"✅ 已清理文件: {target}")
                cleaned_count += 1
            except Exception as e:
                logger.warning(f"⚠️ 清理失败 {target}: {e}")
    
    logger.info(f"🎉 清理完成，共清理了 {cleaned_count} 个项目")
    logger.info("ℹ️ 用户Chrome浏览器数据未受影响")

if __name__ == "__main__":
    safe_cleanup_automation_data()
'''
    
    print("📝 安全清理脚本内容:")
    print("```python")
    print(script_content)
    print("```")
    
    return script_content

def main():
    """主分析函数"""
    
    # 执行完整分析
    directories = analyze_chrome_data_separation()
    isolation = analyze_data_isolation()
    root_causes = analyze_instability_root_causes()
    cleanup_analysis = analyze_cleanup_necessity()
    recommendations = provide_stability_recommendations()
    script = create_safe_cleanup_script()
    
    print(f"\n" + "="*80)
    print("📊 分析结论")
    print("="*80)
    
    conclusions = [
        "🔒 数据完全隔离: Undetected Chrome使用独立配置目录，与用户Chrome无关",
        "❌ 无需清理用户Chrome: 清理用户数据不会解决Undetected Chrome问题",
        "🎯 真正原因: Chrome进程管理、端口冲突、版本兼容等技术问题",
        "✅ 安全清理: 只清理 ~/.linkedin_automation/ 目录下的自动化数据",
        "🔧 最佳策略: 使用现有的混合模式 + 定期清理自动化配置",
        "⚠️ 避免误区: 不要清理用户Chrome数据，会严重影响用户体验"
    ]
    
    for conclusion in conclusions:
        print(f"  {conclusion}")
    
    print(f"\n💡 最终建议:")
    final_recommendations = [
        "1. 保持当前混合模式（Undetected + 标准Selenium）",
        "2. 定期清理自动化配置目录（每周或出现问题时）",
        "3. 绝对不要清理用户Chrome浏览器数据",
        "4. 考虑逐步迁移到更稳定的Playwright方案"
    ]
    
    for rec in final_recommendations:
        print(f"  {rec}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

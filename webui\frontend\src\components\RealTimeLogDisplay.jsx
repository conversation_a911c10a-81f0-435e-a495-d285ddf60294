import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, IconButton, Fade } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const RealTimeLogDisplay = ({ 
  isVisible, 
  onClose, 
  position = { top: 400, left: 240, width: 750, height: 120 } 
}) => {
  const [logs, setLogs] = useState([]);
  const [wsConnection, setWsConnection] = useState(null);
  const logContainerRef = useRef(null);
  const autoCloseTimerRef = useRef(null);

  // WebSocket连接管理
  useEffect(() => {
    if (!isVisible) {
      // 清理WebSocket连接
      if (wsConnection) {
        console.log('🔌 关闭WebSocket连接');
        wsConnection.close();
        setWsConnection(null);
      }
      return;
    }

    console.log('🔌 尝试建立WebSocket连接...');

    // 创建WebSocket连接
    const ws = new WebSocket('ws://localhost:8003/api/linkedin/ws/logs');

    ws.onopen = () => {
      console.log('📡 日志WebSocket连接已建立');
      setWsConnection(ws);

      // 添加一条测试日志
      const testLog = {
        id: `test-${Date.now()}`,
        message: '🔗 WebSocket连接成功，等待日志数据...',
        type: 'info',
        timestamp: Date.now()
      };
      setLogs([testLog]);
    };

    ws.onmessage = (event) => {
      console.log('📨 收到WebSocket消息:', event.data);

      try {
        // 尝试解析JSON格式的消息
        const data = JSON.parse(event.data);

        // 处理生命周期事件
        if (data.event) {
          console.log('🔄 处理生命周期事件:', data.event);
          handleLifecycleEvent(data);
          return;
        }

        // 处理结构化日志消息
        if (data.message) {
          const newLog = {
            id: `${data.timestamp || Date.now()}-${Math.random()}`,
            message: data.message,
            type: data.type || 'info',
            timestamp: data.timestamp || Date.now(),
            isNew: true // 🔧 标记为新日志，用于淡入动画
          };

          console.log('📝 添加结构化日志:', newLog);
          setLogs(prevLogs => {
            const updatedLogs = [...prevLogs, newLog].slice(-50); // 🔧 增加到50条，减少突然消失
            return updatedLogs;
          });

          // 🔧 短暂延迟后移除新日志标记，触发淡入效果
          setTimeout(() => {
            setLogs(prevLogs =>
              prevLogs.map(log =>
                log.id === newLog.id ? { ...log, isNew: false } : log
              )
            );
          }, 50);
          return;
        }
      } catch (error) {
        // 如果不是JSON格式，当作原始日志消息处理
        console.log('📝 处理原始日志消息:', event.data);
        const newLog = {
          id: `${Date.now()}-${Math.random()}`,
          message: event.data,
          type: 'info',
          timestamp: Date.now(),
          isNew: true // 🔧 标记为新日志
        };

        setLogs(prevLogs => {
          const updatedLogs = [...prevLogs, newLog].slice(-50); // 🔧 增加到50条，减少突然消失
          return updatedLogs;
        });

        // 🔧 短暂延迟后移除新日志标记
        setTimeout(() => {
          setLogs(prevLogs =>
            prevLogs.map(log =>
              log.id === newLog.id ? { ...log, isNew: false } : log
            )
          );
        }, 50);
      }
    };

    ws.onerror = (error) => {
      console.error('❌ WebSocket连接错误:', error);

      // 添加错误日志到显示
      const errorLog = {
        id: `error-${Date.now()}`,
        message: '❌ WebSocket连接失败',
        type: 'error',
        timestamp: Date.now()
      };
      setLogs(prevLogs => [...prevLogs, errorLog].slice(-50)); // 🔧 增加到50条
    };

    ws.onclose = (event) => {
      console.log('📡 日志WebSocket连接已关闭, code:', event.code, 'reason:', event.reason);
      setWsConnection(null);
    };

    return () => {
      console.log('🧹 清理WebSocket连接');
      ws.close();
    };
  }, [isVisible]);

  // 处理搜索生命周期事件
  const handleLifecycleEvent = (eventData) => {
    const { event } = eventData;

    console.log('🔄 处理生命周期事件:', event);

    switch (event) {
      case 'search_start':
        // 清空之前的日志，开始新的搜索会话
        setLogs([]);
        // 清除任何现有的自动关闭定时器
        if (autoCloseTimerRef.current) {
          clearTimeout(autoCloseTimerRef.current);
          autoCloseTimerRef.current = null;
        }
        console.log('🔄 搜索开始，清空日志');
        break;

      case 'search_complete':
        // 搜索完成，设置2秒后自动关闭
        console.log('✅ 搜索完成，2秒后自动关闭');
        autoCloseTimerRef.current = setTimeout(() => {
          console.log('⏰ 自动关闭日志显示');
          onClose();
        }, 2000);
        break;

      case 'search_error':
        // 搜索出错，设置3秒后自动关闭（给用户更多时间查看错误）
        console.log('❌ 搜索出错，3秒后自动关闭');
        autoCloseTimerRef.current = setTimeout(() => {
          console.log('⏰ 自动关闭日志显示（错误）');
          onClose();
        }, 3000);
        break;
    }
  };

  // 自动滚动到底部
  useEffect(() => {
    if (logContainerRef.current && logs.length > 0) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (autoCloseTimerRef.current) {
        clearTimeout(autoCloseTimerRef.current);
      }
    };
  }, []);

  // 手动关闭处理
  const handleManualClose = () => {
    if (autoCloseTimerRef.current) {
      clearTimeout(autoCloseTimerRef.current);
      autoCloseTimerRef.current = null;
    }
    onClose();
  };

  if (!isVisible) return null;

  return (
    <Fade in={isVisible} timeout={300}>
      <Box
        sx={{
          position: 'fixed',
          top: position.top,
          left: position.left,
          width: position.width,
          height: position.height,
          zIndex: 1500,
          background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)',
          border: '1px solid #333',
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.8)',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          animation: 'slideInFromTop 0.3s ease-out',
          '@keyframes slideInFromTop': {
            '0%': {
              opacity: 0,
              transform: 'translateY(-20px) scale(0.95)'
            },
            '100%': {
              opacity: 1,
              transform: 'translateY(0) scale(1)'
            }
          }
        }}
      >
        {/* 标题栏 */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 1.5,
          borderBottom: '1px solid #333',
          backgroundColor: '#1a1a1a'
        }}>
          <Typography 
            variant="subtitle2" 
            sx={{ 
              color: '#00d4ff',
              fontWeight: 600,
              fontSize: '0.875rem'
            }}
          >
            ✨机器人🤖正在为您马不停蹄地效劳...
          </Typography>
          <IconButton
            size="small"
            onClick={handleManualClose}
            sx={{
              color: '#666',
              '&:hover': {
                color: '#00d4ff',
                backgroundColor: 'rgba(0, 212, 255, 0.1)'
              }
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </Box>

        {/* 日志内容区域 */}
        <Box
          ref={logContainerRef}
          sx={{
            flex: 1,
            overflow: 'auto',
            p: 1,
            backgroundColor: '#0a0a0a',
            // 🔧 添加平滑滚动效果
            scrollBehavior: 'smooth',
            transition: 'all 0.3s ease-out',
            '&::-webkit-scrollbar': {
              width: '6px'
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: '#1a1a1a'
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#333',
              borderRadius: '3px',
              '&:hover': {
                backgroundColor: '#555'
              }
            }
          }}
        >
          {logs.length === 0 ? (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: '#666',
              fontSize: '0.8rem'
            }}>
              等待日志信息...
            </Box>
          ) : (
            logs.map((log) => (
              <Box
                key={log.id}
                sx={{
                  color: getLogColor(log.type),
                  fontSize: '0.8rem',
                  fontFamily: 'inherit',
                  mb: 0.5,
                  p: 0.5,
                  borderRadius: 1,
                  backgroundColor: 'transparent',
                  // 🔧 平滑淡入效果：新日志从透明到不透明
                  opacity: log.isNew ? 0 : 1,
                  transform: log.isNew ? 'translateY(10px)' : 'translateY(0)',
                  transition: 'all 0.3s ease-out'
                }}
              >
                {log.message}
              </Box>
            ))
          )}
        </Box>
      </Box>
    </Fade>
  );
};

// 根据日志类型获取颜色
const getLogColor = (type) => {
  switch (type) {
    case 'success':
      return '#4caf50';
    case 'error':
      return '#f44336';
    case 'start':
      return '#00d4ff';
    default:
      return '#e0e0e0';
  }
};

export default RealTimeLogDisplay;

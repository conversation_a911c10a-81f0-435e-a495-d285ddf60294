# In this file, you can set the configurations of the app.
from pathlib import Path

DATA_FOLDER_PATH = Path(__file__).parent / 'data_folder'

# Import constants if needed
# from src.utils.constants import DEBUG, ERROR, LLM_MODEL, OPENAI

#config related to logging must have prefix LOG_
LOG_LEVEL = 'INFO'
LOG_SELENIUM_LEVEL = 'INFO'
LOG_TO_FILE = True
LOG_TO_CONSOLE = True

MINIMUM_WAIT_TIME_IN_SECONDS = 60

JOB_APPLICATIONS_DIR = "job_applications"
JOB_SUITABILITY_SCORE = 15

JOB_MAX_APPLICATIONS = 7
JOB_MIN_APPLICATIONS = 1

LLM_MODEL_TYPE = 'gemini'
LLM_MODEL = 'gemini-2.5-flash-preview-0514'  # Updated to latest model
LLM_TEMPERATURE = 1.0
LLM_TIMEOUT = 60
LLM_MAX_RETRIES = 3

# Only required for OLLAMA models
LLM_API_URL = ''

# LinkedIn Automation Settings
LINKEDIN_AUTOMATION_MODE = 'selenium'  # 'selenium' (recommended) or 'playwright'
LINKEDIN_MAX_PAGES = 5
LINKEDIN_APPLICATION_TIMEOUT = 600  # 10 minutes
LINKEDIN_SCROLL_ROUNDS = 3

# Chrome Settings
CHROME_CLEANUP_ENABLED = True
CHROME_HEADLESS = False

# WebUI Settings
FRONTEND_PORT = 3000
BACKEND_PORT = 8003
WEBSOCKET_ENABLED = True

# Performance Settings
MAX_CONCURRENT_APPLICATIONS = 1
APPLICATION_DELAY_RANGE = [30, 60]  # seconds

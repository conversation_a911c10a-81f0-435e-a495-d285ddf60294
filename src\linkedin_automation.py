"""LinkedIn自动化模块
实现LinkedIn登录验证、搜索职位、筛选Easy Apply职位并进行投递的功能
"""

import time
import random
from datetime import datetime
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    NoSuchElementException, 
    TimeoutException, 
    ElementClickInterceptedException,
    StaleElementReferenceException
)
from selenium.webdriver.chrome.options import Options
from loguru import logger
import yaml
import os
from pathlib import Path
# 🔧 已移除 LinkedInJobDetector 导入（简化处理逻辑）

class LinkedInAutomation:
    """LinkedIn自动化类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化LinkedIn自动化实例
        
        Args:
            config_path: 配置文件路径
        """
        self.driver = None
        self.wait = None
        self.config = self._load_config(config_path)
        self.is_logged_in = False
        self.applied_jobs = set()  # 记录已申请的职位
        self.log_dir = Path(__file__).parent.parent / "log"
        self.log_dir.mkdir(parents=True, exist_ok=True)
        # 🔧 已移除 job_detector（简化处理逻辑）
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件，优先使用用户设置文件"""
        # 首先尝试加载用户设置文件
        user_settings_path = Path(__file__).resolve().parents[1] / "data_folder" / "user_settings.yaml"
        config = {}

        if user_settings_path.exists():
            try:
                with open(user_settings_path, 'r', encoding='utf-8') as f:
                    user_settings = yaml.safe_load(f)
                    if user_settings:
                        config = user_settings
                        logger.info(f"已加载用户设置文件: {user_settings_path}")
            except Exception as e:
                logger.warning(f"加载用户设置文件失败: {str(e)}")

        # 如果指定了配置文件路径，则合并配置
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    custom_config = yaml.safe_load(f)
                    if custom_config:
                        # 深度合并配置
                        config = self._merge_configs(config, custom_config)
                        logger.info(f"已合并自定义配置文件: {config_path}")
            except Exception as e:
                logger.warning(f"加载自定义配置文件失败: {str(e)}")

        # 如果没有任何配置，使用默认配置
        if not config:
            config = self._get_default_config()
            logger.info("使用默认配置")

        # 确保必要的配置项存在
        return self._ensure_config_completeness(config)

    def _merge_configs(self, base_config: Dict, override_config: Dict) -> Dict:
        """深度合并两个配置字典"""
        result = base_config.copy()

        for key, value in override_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value

        return result

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer', 'software engineer'],
                'location': 'United States',
                'experience_level': ['Entry level', 'Associate', 'Mid-Senior level'],
                'job_type': ['Full-time', 'Part-time', 'Contract'],
                'remote_work': ['On-site', 'Remote', 'Hybrid'],
                'automation_type': 'selenium',
                'application': {
                    'max_applications_per_day': 50,
                    'delay_between_applications': [5, 10],  # 秒 - 优化申请速度
                    'auto_answer_questions': True,
                    'default_answers': {
                        'years_experience': '15',
                        'willing_to_relocate': 'Yes',
                        'authorized_to_work': 'Yes',
                        'require_sponsorship': 'No',
                        'expected_salary': 'Negotiable面议',
                        'start_date': 'Immediately立即'
                    }
                }
            },
            'selenium': {
                'headless': False,
                'implicit_wait': 10,
                'page_load_timeout': 30,
                'window_size': [1920, 1080],
                'use_undetected': True
            }
        }

    def _ensure_config_completeness(self, config: Dict) -> Dict:
        """确保配置的完整性，补充缺失的配置项"""
        default_config = self._get_default_config()
        return self._merge_configs(default_config, config)

    def force_cleanup_chrome(self):
        """🔧 强制清理Chrome资源 - 解决连接问题的工具方法"""
        logger.info("🧹 用户请求强制清理Chrome资源...")

        try:
            # 关闭当前驱动
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None

            # 使用undetected_chrome_utils的清理功能
            from src.utils.undetected_chrome_utils import force_cleanup_chrome
            force_cleanup_chrome()

            logger.info("✅ Chrome资源清理完成，可以重新尝试操作")
            return True

        except Exception as e:
            logger.error(f"❌ Chrome资源清理失败: {e}")
            return False
    
    def setup_driver(self, headless: bool = None, use_undetected: bool = None) -> webdriver.Chrome:
        """设置Chrome WebDriver，优先使用Undetected ChromeDriver - 改进配置路径和错误处理"""
        if headless is None:
            # 支持新旧配置格式
            if 'browser' in self.config.get('linkedin', {}):
                headless = self.config['linkedin']['browser'].get('headless', False)
            else:
                headless = self.config.get('selenium', {}).get('headless', False)

        if use_undetected is None:
            # 支持新旧配置格式
            if 'browser' in self.config.get('linkedin', {}):
                use_undetected = self.config['linkedin']['browser'].get('use_undetected', True)
            else:
                use_undetected = self.config.get('selenium', {}).get('use_undetected', True)

        # 尝试使用Undetected ChromeDriver
        if use_undetected:
            try:
                from src.utils.undetected_chrome_utils import UndetectedChromeManager, check_undetected_availability

                if check_undetected_availability():
                    logger.info("使用Undetected ChromeDriver进行反检测优化...")

                    # 改进的用户配置目录设置 - 确保路径正确且可访问
                    profile_path = Path.home() / ".linkedin_automation" / "chrome_profile"

                    # 确保配置目录存在且有正确权限
                    try:
                        profile_path.mkdir(parents=True, exist_ok=True)
                        logger.info(f"使用Chrome配置目录: {profile_path}")

                        # 测试目录是否可写
                        test_file = profile_path / "test_write.tmp"
                        test_file.write_text("test")
                        test_file.unlink()
                        logger.debug("配置目录写入权限验证成功")

                    except Exception as e:
                        logger.warning(f"配置目录设置失败: {e}")
                        # 使用临时目录作为备选
                        import tempfile
                        profile_path = Path(tempfile.mkdtemp(prefix="linkedin_chrome_"))
                        logger.info(f"使用临时配置目录: {profile_path}")

                    manager = UndetectedChromeManager()
                    
                    # 🔧 增强的Chrome启动策略 - 解决连接问题
                    max_chrome_attempts = 3
                    chrome_created = False
                    
                    for chrome_attempt in range(max_chrome_attempts):
                        try:
                            logger.info(f"尝试启动Chrome (第{chrome_attempt+1}/{max_chrome_attempts}次)")
                            
                            # 如果不是第一次尝试，先进行强制清理
                            if chrome_attempt > 0:
                                logger.info("执行强制清理后重试...")
                                manager.force_cleanup_all()
                                time.sleep(3)
                            
                            self.driver = manager.create_driver(
                                headless=headless,
                                profile_path=str(profile_path),
                                version_main=137
                            )
                            
                            # 验证Chrome是否真正可用
                            try:
                                self.driver.get("data:text/html,<html><body>Chrome启动测试</body></html>")
                                logger.info("✅ Chrome启动验证成功")
                                chrome_created = True
                                break
                            except Exception as verify_error:
                                logger.warning(f"Chrome启动验证失败: {verify_error}")
                                if self.driver:
                                    try:
                                        self.driver.quit()
                                    except:
                                        pass
                                    self.driver = None
                                raise verify_error
                                
                        except Exception as chrome_error:
                            logger.warning(f"Chrome启动失败 (第{chrome_attempt+1}次): {chrome_error}")
                            
                            if chrome_attempt == max_chrome_attempts - 1:
                                logger.error("Chrome启动彻底失败，尝试使用标准ChromeDriver")
                                raise chrome_error
                            else:
                                # 等待后重试
                                time.sleep(2)
                    
                    if not chrome_created:
                        raise RuntimeError("Chrome启动失败，已尝试所有方法")

                    # 设置等待和超时
                    self.driver.implicitly_wait(self.config['selenium']['implicit_wait'])
                    self.driver.set_page_load_timeout(self.config['selenium']['page_load_timeout'])

                    self.wait = WebDriverWait(self.driver, 20)
                    self._chrome_manager = manager  # 保存管理器引用

                    logger.info("Undetected ChromeDriver设置成功")

                    # 验证浏览器窗口是否正常显示
                    if not headless:
                        try:
                            window_size = self.driver.get_window_size()
                            logger.info(f"浏览器窗口大小: {window_size}")
                            if window_size['width'] > 0 and window_size['height'] > 0:
                                logger.info("浏览器窗口正常显示")
                            else:
                                logger.warning("浏览器窗口可能未正常显示")
                        except Exception as e:
                            logger.warning(f"检查浏览器窗口时出错: {e}")

                    # 🔧 已移除职位检测器初始化（简化处理逻辑）
                    return self.driver
                else:
                    logger.warning("Undetected ChromeDriver不可用，回退到标准Selenium")
            except Exception as e:
                logger.warning(f"Undetected ChromeDriver设置失败，回退到标准Selenium: {str(e)}")
                # 清理失败的驱动
                if hasattr(self, 'driver') and self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None

        # 回退到标准Selenium设置
        logger.info("使用标准Selenium ChromeDriver...")
        driver = self._setup_standard_driver(headless)
        # 🔧 已移除职位检测器初始化（简化处理逻辑）
        return driver

    def _setup_standard_driver(self, headless: bool) -> webdriver.Chrome:
        """设置标准Selenium ChromeDriver（回退方案）- 改进版本"""
        logger.info("设置标准Selenium ChromeDriver...")

        # 使用优化后的chrome_browser_options函数
        from src.utils.chrome_utils import chrome_browser_options
        chrome_options = chrome_browser_options()

        if headless:
            chrome_options.add_argument('--headless=new')  # 使用新的headless模式
        else:
            chrome_options.add_argument('--start-maximized')

        # 窗口大小
        window_size = self.config['selenium']['window_size']
        chrome_options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')

        # 改进的反检测设置 - 移除可能导致问题的选项
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        # 注释掉可能不兼容的选项
        # chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        # chrome_options.add_experimental_option('useAutomationExtension', False)

        # 添加用户配置目录支持（保持登录状态）
        profile_path = Path.home() / ".linkedin_automation" / "chrome_profile_selenium"
        try:
            profile_path.mkdir(parents=True, exist_ok=True)
            chrome_options.add_argument(f'--user-data-dir={profile_path}')
            logger.info(f"使用Selenium配置目录: {profile_path}")
        except Exception as e:
            logger.warning(f"设置Selenium配置目录失败: {e}")

        # 添加更多稳定性选项
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument('--disable-popup-blocking')

        # 设置用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        try:
            # 使用 webdriver_manager 自动管理 ChromeDriver
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # 应用反检测脚本
            try:
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                logger.debug("应用反检测脚本成功")
            except Exception as e:
                logger.warning(f"应用反检测脚本失败: {e}")

            # 设置等待和超时 - 优化申请流程速度
            self.driver.implicitly_wait(3)  # 减少隐式等待到3秒
            self.driver.set_page_load_timeout(self.config['selenium']['page_load_timeout'])

            self.wait = WebDriverWait(self.driver, 8)  # 减少显式等待到8秒
            
            logger.info("Chrome WebDriver 设置完成")
            return self.driver
        except Exception as e:
            logger.error(f"设置Chrome WebDriver失败: {str(e)}")
            raise RuntimeError(f"设置Chrome WebDriver失败: {str(e)}")
    
    # 🔧 已移除 _initialize_job_detector 方法（简化处理逻辑）
    
    def login(self, email: str = None, password: str = None) -> Dict:
        """🔧 优化登录LinkedIn - 优先直接访问Feed页面检查登录状态"""
        try:
            if not email:
                email = self.config['linkedin'].get('email', '')
            if not password:
                password = self.config['linkedin'].get('password', '')
            if not email or not password:
                logger.error("未提供LinkedIn账号或密码")
                return {"success": False, "status": "未提供账号或密码", "requires_action": False}

            logger.info(f"🔐 开始LinkedIn登录检查... 使用邮箱: {email[:3]}****{email[-10:]}")

            # 🔧 优先直接访问LinkedIn Feed页面检查登录状态
            logger.info("📱 优先直接访问LinkedIn Feed页面...")
            try:
                self.driver.get("https://www.linkedin.com/feed")
                time.sleep(3)  # 增加等待时间确保页面完全加载
            except Exception as e:
                logger.error(f"❌ 访问LinkedIn Feed页面失败: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn", "requires_action": False}

            # 等待页面加载完成
            try:
                self.wait.until(lambda d: "linkedin.com" in d.current_url)
                time.sleep(2)  # 额外等待确保页面状态稳定
            except Exception:
                logger.warning("⚠️ 页面加载等待超时")

            # 检查当前URL和页面状态
            current_url = self.driver.current_url
            logger.info(f"📍 当前页面URL: {current_url}")

            # 🔧 优先检查是否已经在Feed页面（表示已登录）
            if "linkedin.com/feed" in current_url:
                logger.info("✅ 已在LinkedIn Feed页面，确认登录状态...")
                # 进一步验证是否真的已登录（检查页面元素）
                try:
                    # 检查是否有登录用户的导航栏
                    nav_elements = self.driver.find_elements(By.CSS_SELECTOR, ".global-nav, .feed-identity-module")
                    if nav_elements and any(el.is_displayed() for el in nav_elements):
                        logger.info("✅ 检测到用户导航栏，确认已登录")
                        self.is_logged_in = True
                        return {"success": True, "status": "已登录", "requires_action": False}
                except Exception as e:
                    logger.debug(f"检查导航栏时出错: {e}")

            # 检查是否被重定向到登录页面
            elif "login" in current_url:
                logger.info("🔄 被重定向到登录页面，需要登录")
            # 检查是否需要验证
            elif "challenge" in current_url or "checkpoint" in current_url:
                logger.warning("🔐 检测到需要验证")
                return {"success": False, "status": "需要完成验证，请在浏览器中完成验证", "requires_action": True}
            else:
                logger.info(f"📍 当前在其他LinkedIn页面: {current_url}")
                # 尝试再次访问Feed页面
                logger.info("🔄 尝试再次访问Feed页面...")
                self.driver.get("https://www.linkedin.com/feed")
                time.sleep(3)
                current_url = self.driver.current_url
                if "linkedin.com/feed" in current_url:
                    logger.info("✅ 成功访问Feed页面，已登录")
                    self.is_logged_in = True
                    return {"success": True, "status": "已登录", "requires_action": False}

            # 🔐 需要登录，进入登录流程
            logger.info("🔐 检测到需要登录，进入登录流程...")
            try:
                self.driver.get("https://www.linkedin.com/login")
                time.sleep(3)

                # 填写登录表单
                from selenium.webdriver.common.by import By
                email_input = self.driver.find_element(By.ID, "username")
                password_input = self.driver.find_element(By.ID, "password")
                email_input.clear()
                email_input.send_keys(email)
                password_input.clear()
                password_input.send_keys(password)
                login_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
                login_btn.click()

                logger.info("📝 已提交登录表单，等待结果...")
                time.sleep(8)

            except Exception as e:
                logger.error(f"❌ 登录表单填写失败: {str(e)}")
                return {"success": False, "status": "登录表单填写失败", "requires_action": False}

            # 🔍 检查登录结果
            final_url = self.driver.current_url
            logger.info(f"📍 登录后URL: {final_url}")

            if "linkedin.com/feed" in final_url:
                logger.info("✅ 登录成功，已进入Feed页面")
                self.is_logged_in = True
                return {"success": True, "status": "登录成功", "requires_action": False}
            elif "checkpoint" in final_url or "challenge" in final_url:
                logger.warning("🔐 需要额外验证")
                return {"success": False, "status": "需要额外验证", "requires_action": True}
            elif "login" in final_url:
                logger.error("❌ 登录失败，仍在登录页面")
                return {"success": False, "status": "登录失败，请检查账号密码", "requires_action": False}
            else:
                # 尝试访问Feed页面验证登录状态
                logger.info("🔄 尝试访问Feed页面验证登录状态...")
                self.driver.get("https://www.linkedin.com/feed")
                time.sleep(3)
                if "linkedin.com/feed" in self.driver.current_url:
                    logger.info("✅ 登录成功")
                    self.is_logged_in = True
                    return {"success": True, "status": "登录成功", "requires_action": False}
                else:
                    logger.error("❌ 登录状态不明确")
                    return {"success": False, "status": "登录状态不明确", "requires_action": False}

        except Exception as e:
            logger.error(f"❌ 登录过程发生未知错误: {str(e)}")
            return {"success": False, "status": "登录过程发生未知错误，请稍后重试", "requires_action": False}

    def verify_login_status(self) -> Dict:
        """🔧 优化的登录状态验证 - 优先直接访问Feed页面检查登录状态

        Returns:
            包含验证结果的字典
        """
        try:
            logger.info("🔍 开始验证LinkedIn登录状态...")

            # 🔧 优先直接访问LinkedIn Feed页面检查登录状态
            logger.info("📱 直接访问LinkedIn Feed页面检查登录状态...")
            try:
                self.driver.get("https://www.linkedin.com/feed")
                time.sleep(3)  # 等待页面加载
            except Exception as e:
                logger.error(f"❌ 访问LinkedIn Feed页面失败: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn", "requires_action": False}

            # 检查当前URL
            current_url = self.driver.current_url
            logger.info(f"📍 访问Feed后的URL: {current_url}")

            # 🔧 如果成功访问Feed页面，说明已登录
            if "linkedin.com/feed" in current_url:
                logger.info("✅ 成功访问LinkedIn Feed页面，确认已登录")
                # 进一步验证页面元素确保真的已登录
                try:
                    nav_elements = self.driver.find_elements(By.CSS_SELECTOR, ".global-nav, .feed-identity-module, .nav-item__profile-member-photo")
                    if nav_elements and any(el.is_displayed() for el in nav_elements):
                        logger.info("✅ 检测到用户导航元素，确认登录状态")
                        self.is_logged_in = True
                        return {"success": True, "status": "已登录", "requires_action": False}
                except Exception as e:
                    logger.debug(f"检查导航元素时出错: {e}")

                # 即使没有找到导航元素，如果在Feed页面也认为已登录
                logger.info("✅ 在Feed页面，认为已登录")
                self.is_logged_in = True
                return {"success": True, "status": "已登录", "requires_action": False}

            # 如果被重定向到登录页面，说明未登录
            elif "login" in current_url:
                logger.info("🔄 被重定向到登录页面，未登录")
                self.is_logged_in = False
                return {"success": False, "status": "未登录", "requires_action": False}

            # 检查是否需要验证
            elif "challenge" in current_url or "checkpoint" in current_url:
                logger.warning("🔐 检测到需要验证")
                return {"success": False, "status": "需要完成验证，请在浏览器中完成验证", "requires_action": True}

            # 如果在其他LinkedIn页面，检查登录状态
            else:
                logger.info(f"📍 在其他LinkedIn页面: {current_url}")
                # 检查是否有登录标识元素
                try:
                    login_indicators = [
                        ".global-nav",
                        ".feed-identity-module",
                        "[data-control-name='nav.settings']",
                        ".nav-item__profile-member-photo",
                        ".global-nav__me"
                    ]

                    for indicator in login_indicators:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                            if elements and any(el.is_displayed() for el in elements):
                                logger.info(f"✅ 找到登录标识元素: {indicator}")
                                self.is_logged_in = True
                                return {"success": True, "status": "已登录", "requires_action": False}
                        except:
                            continue

                    logger.info("⚠️ 未找到明确的登录标识")
                    self.is_logged_in = False
                    return {"success": False, "status": "登录状态不明确", "requires_action": False}

                except Exception as e:
                    logger.debug(f"检查登录标识时出错: {e}")
                    self.is_logged_in = False
                    return {"success": False, "status": "登录状态检查失败", "requires_action": False}

        except Exception as e:
            logger.error(f"❌ 验证登录状态时发生错误: {str(e)}")
            return {"success": False, "status": "验证登录状态时发生错误", "requires_action": False}

    # 🔧 已移除 _clear_old_snapshots 方法（不再使用网页快照方式）

    def search_jobs(self, keywords: str = None, location: str = None,
                   easy_apply_only: bool = True) -> List[Dict]:
        """搜索职位"""
        # 每次搜索前，重置职位列表以确保数据隔离
        self.all_jobs = []
        logger.info("🔄 已重置内部职位列表，开始新的搜索任务。")

        try:
            if not self.is_logged_in:
                logger.error("请先登录LinkedIn")
                return []

            if not keywords:
                keywords = self.config['linkedin']['search_keywords'][0]
            if not location:
                location = self.config['linkedin']['location']

            logger.info(f"搜索职位: {keywords} in {location}")

            # 🔧 已移除快照文件清理（不再使用网页快照方式）

            # 温和的缓存清理，保持登录状态
            logger.info("🧹 清理搜索缓存（保持登录状态）...")
            try:
                # 只清理搜索相关的存储，不删除登录cookies
                self.driver.execute_script("""
                    // 只清理搜索相关的localStorage项
                    for (let i = localStorage.length - 1; i >= 0; i--) {
                        const key = localStorage.key(i);
                        if (key && (key.includes('search') || key.includes('job') || key.includes('filter'))) {
                            localStorage.removeItem(key);
                        }
                    }

                    // 只清理搜索相关的sessionStorage项
                    for (let i = sessionStorage.length - 1; i >= 0; i--) {
                        const key = sessionStorage.key(i);
                        if (key && (key.includes('search') || key.includes('job') || key.includes('filter'))) {
                            sessionStorage.removeItem(key);
                        }
                    }
                """)
                logger.info("✅ 已清理搜索相关缓存，保持登录状态")
            except Exception as e:
                logger.debug(f"清理搜索缓存时出错: {e}")

            # 构建搜索URL
            search_url = f"https://www.linkedin.com/jobs/search/?keywords={keywords}&location={location}"
            if easy_apply_only:
                search_url += "&f_AL=true"  # Easy Apply筛选

            logger.info(f"🔍 开始搜索: {search_url}")
            self.driver.get(search_url)
            time.sleep(5)  # 等待页面完全加载

            # 🔧 简化：直接进入分页处理，滚动逻辑已集成到页面处理中

            # 4. 处理分页并收集职位
            all_jobs = self._handle_pagination_and_collect_jobs(keywords, location)
            if all_jobs:
                logger.info(f"✅ 通过分页处理成功提取 {len(all_jobs)} 个职位信息")

                # 🔧 确保所有职位字段名匹配JobInfo模型
                all_jobs = self._normalize_job_fields(all_jobs)

                # 5. 最终LLM汇总筛选
                logger.info("🤖 开始最终LLM汇总筛选...")
                final_jobs = self._final_llm_filter_all_jobs(all_jobs, keywords, location)
                logger.info(f"✅ 最终筛选完成，返回 {len(final_jobs)} 个优质职位")

                # 🔧 再次确保最终结果字段名正确
                final_jobs = self._normalize_job_fields(final_jobs)

                return final_jobs
            
            # 使用LLM解析当前页面职位信息
            jobs = self._parse_jobs_with_llm(self.driver.page_source, keywords, location)

            if not jobs:
                logger.warning("⚠️ LLM解析未找到职位，返回空列表")
                jobs = []

            logger.info(f"✅ 成功提取 {len(jobs)} 个职位信息")
            return jobs

        except Exception as e:
            logger.error(f"❌ 搜索职位失败: {str(e)}", exc_info=True)
            return []

    def _scroll_job_list_basic(self):
        """滚动职位列表以加载所有职位（基础版本 - 已禁用）"""
        logger.warning("⚠️ 基础版本的_scroll_job_list_basic已被禁用，请使用增强版本的_scroll_job_list")
        return False
        
        # 以下代码已被禁用
        try:
            logger.info("🔄 开始滚动职位列表...")
            scroll_container_selector = ".jobs-search-results-list"
            scroll_container = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, scroll_container_selector))
            )
            logger.info(f"✅ 找到滚动容器: {scroll_container_selector}")

            last_height = 0
            attempts = 0
            max_attempts = 30 # 最多尝试30次

            while attempts < max_attempts:
                self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", scroll_container)
                time.sleep(random.uniform(2.0, 3.0)) # 增加等待时间，确保懒加载完成

                new_height = self.driver.execute_script("return arguments[0].scrollHeight", scroll_container)
                
                if new_height == last_height:
                    logger.info("✅ 滚动到底部，所有职位已加载。")
                    break
                
                last_height = new_height
                attempts += 1
                logger.debug(f"滚动第 {attempts} 次, 当前高度: {new_height}")

            if attempts == max_attempts:
                logger.warning("达到最大滚动次数，可能未加载全部职位。")

        except TimeoutException:
            logger.error("❌ 未找到职位列表的滚动容器。页面可能已更改或加载失败。")
        except Exception as e:
            logger.error(f"❌ 滚动职位列表时出错: {e}")

    def _wait_for_jobs_to_load(self, timeout: int = 40) -> bool:
        """
        等待职位列表加载完成
        
        Args:
            timeout: 最大等待时间（秒）- 增加到40秒以适应渐进式滚动加载
            
        Returns:
            bool: 是否成功加载
        """
        try:
            # 尝试多个可能的职位列表选择器
            selectors = [
                ".jobs-search-results-list",
                ".jobs-search-results__list",
                "#main > div > div.scaffold-layout__list",
                ".jobs-search-results",
                ".scaffold-layout__list-item"  # 添加主要选择器
            ]
            
            # 分阶段等待策略
            initial_timeout = min(15, timeout // 2)  # 初始快速检测
            extended_timeout = timeout - initial_timeout  # 扩展等待时间
            
            # 第一阶段：快速检测基本列表容器
            for selector in selectors[:4]:  # 前4个是容器选择器
                try:
                    self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector)), timeout=initial_timeout
                    )
                    logger.info(f"✅ 找到职位列表容器: {selector}")
                    break
                except:
                    continue
            
            # 第二阶段：等待具体职位项加载（适应渐进式滚动）
            start_time = time.time()
            while time.time() - start_time < extended_timeout:
                try:
                    # 检查是否有职位项加载
                    job_items = self.driver.find_elements(By.CSS_SELECTOR, ".scaffold-layout__list-item")
                    if len(job_items) > 0:
                        logger.info(f"✅ 检测到 {len(job_items)} 个职位项已加载")
                        return True
                    
                    # 短暂等待后重试
                    time.sleep(2)
                except Exception as e:
                    logger.debug(f"等待职位项时出错: {e}")
                    time.sleep(1)
                    
            logger.warning(f"⚠️ 职位列表加载超时（{timeout}秒），但可能仍有部分内容可用")
            return True  # 改为返回True，允许继续处理已加载的内容
            
        except Exception as e:
            logger.error(f"❌ 等待职位列表加载时出错: {e}")
            return False

    def _get_job_count(self) -> int:
        """获取当前页面上的职位数量"""
        try:
            # 🔧 简化：直接使用主要选择器
            primary_selector = ".scaffold-layout__list-item"
            elements = self.driver.find_elements(By.CSS_SELECTOR, primary_selector)
            visible_elements = [elem for elem in elements if elem.is_displayed()]
            count = len(visible_elements)
            logger.info(f"✅ 找到 {count} 个职位")
            return count
            
        except Exception as e:
            logger.error(f"❌ 获取职位数量时出错: {e}")
            return 0

    def _detect_loading_mode(self) -> bool:
        """检测LinkedIn页面的加载模式
        
        Returns:
            bool: True表示静态加载模式，False表示动态加载模式
        """
        # 🔧 简化：LinkedIn职位搜索已确认为动态懒加载模式，无需重复检测
        logger.info("🔍 LinkedIn职位页面为动态懒加载模式")
        return False  # 动态加载模式
    

    
    def _scroll_job_list(self) -> bool:
        """🔧 简化的滚动方法 - 滚动逻辑已集成到页面处理中"""
        # 滚动逻辑已经集成到 _trigger_lazy_loading_for_all_jobs 中
        # 这里保留方法以保持兼容性，但实际滚动在页面处理时执行
        logger.info("🔄 滚动逻辑已集成到页面处理中，跳过独立滚动步骤")
        return True









    def _check_pagination_visible(self):
        """检查分页控件是否可见"""
        try:
            pagination_selectors = [
                ".jobs-search-pagination",
                ".jobs-search-results__pagination",
                ".artdeco-pagination"
            ]

            for selector in pagination_selectors:
                try:
                    pagination = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if pagination and pagination.is_displayed():
                        return True
                except:
                    continue
            return False
        except:
            return False

    def _check_recommendation_section(self):
        """检测是否已滚动到推荐职位区域"""
        try:
            # 检测推荐职位区域的标识性文本和元素
            recommendation_indicators = [
                # 推荐职位标题文本
                "//h2[contains(text(), 'Top job picks for you')]",
                "//h2[contains(text(), 'Based on your profile')]",
                "//h2[contains(text(), 'Recommended for you')]",
                "//h2[contains(text(), 'More jobs for you')]",
                "//h3[contains(text(), 'Top job picks for you')]",
                "//h3[contains(text(), 'Based on your profile')]",
                
                # 推荐职位描述文本
                "//p[contains(text(), 'Based on your profile, preferences, and activity like applies, searches, and saves')]",
                "//span[contains(text(), 'Based on your profile, preferences, and activity')]",
                "//div[contains(text(), 'profile, preferences, and activity')]",
                
                # 推荐职位容器
                "//div[contains(@class, 'job-recommendations')]",
                "//section[contains(@class, 'recommendations')]",
                "//div[contains(@data-test-id, 'recommendations')]",
                
                # 其他可能的推荐区域标识
                "//div[contains(@class, 'job-picks')]",
                "//section[contains(@class, 'job-picks')]"
            ]
            
            for selector in recommendation_indicators:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            # 检查元素是否在当前视窗内
                            location = element.location
                            size = element.size
                            viewport_height = self.driver.execute_script("return window.innerHeight")
                            scroll_top = self.driver.execute_script("return window.pageYOffset")
                            
                            element_top = location['y']
                            element_bottom = element_top + size['height']
                            viewport_bottom = scroll_top + viewport_height
                            
                            # 如果推荐区域元素在视窗内，说明已滚动到推荐区域
                            if element_top <= viewport_bottom and element_bottom >= scroll_top:
                                logger.info(f"🚫 检测到推荐职位区域元素: {selector}")
                                return True
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            logger.debug(f"检测推荐职位区域时出错: {str(e)}")
            return False
    
    def _perform_progressive_scroll(self, container, scroll_step: int = 600) -> bool:
        """执行渐进式滚动
        
        Args:
            container: 滚动容器元素（可为None表示滚动整个页面）
            scroll_step: 每次滚动的像素距离
            
        Returns:
            bool: 是否成功滚动且未到底部
        """
        try:
            if container:
                # 获取当前滚动位置和容器高度
                current_scroll = self.driver.execute_script(
                    "return arguments[0].scrollTop", container
                )
                scroll_height = self.driver.execute_script(
                    "return arguments[0].scrollHeight", container
                )
                client_height = self.driver.execute_script(
                    "return arguments[0].clientHeight", container
                )
                
                # 计算新的滚动位置（渐进式滚动）
                new_scroll_position = min(current_scroll + scroll_step, scroll_height - client_height)
                
                # 执行渐进式滚动
                self.driver.execute_script(
                    "arguments[0].scrollTop = arguments[1]", 
                    container, new_scroll_position
                )
                
                logger.debug(f"📜 容器滚动: {current_scroll} -> {new_scroll_position} (步长: {scroll_step})")
                
                # 检查是否已滚动到底部
                if new_scroll_position >= scroll_height - client_height:
                    logger.debug("📜 容器已滚动到底部")
                    return False  # 表示已到底部，无需继续滚动
                    
            else:
                # 渐进式滚动整个页面
                current_scroll = self.driver.execute_script("return window.pageYOffset")
                page_height = self.driver.execute_script("return document.body.scrollHeight")
                window_height = self.driver.execute_script("return window.innerHeight")
                
                # 计算新的滚动位置
                new_scroll_position = min(current_scroll + scroll_step, page_height - window_height)
                
                # 执行渐进式滚动
                self.driver.execute_script(f"window.scrollTo(0, {new_scroll_position});")
                
                logger.debug(f"📜 页面滚动: {current_scroll} -> {new_scroll_position} (步长: {scroll_step})")
                
                # 检查是否已滚动到底部
                if new_scroll_position >= page_height - window_height:
                    logger.debug("📜 页面已滚动到底部")
                    return False  # 表示已到底部，无需继续滚动
                    
            return True  # 成功滚动且未到底部
            
        except Exception as e:
            logger.debug(f"渐进式滚动失败: {e}")
            return False



    def _ensure_pagination_visible(self):
        """简化版：确保分页控件可见"""
        try:
            logger.info("🔍 确保分页控件可见...")

            # 直接滚动到页面底部，简单有效
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)

            logger.info("✅ 分页控件现在应该可见")

        except Exception as e:
            logger.warning(f"❌ 确保分页控件可见时出错: {str(e)}")



    def _click_show_more_jobs_button(self):
        """点击"显示更多职位"按钮 - LinkedIn现在主要使用数字分页，此方法保留作为备用"""
        try:
            # 只保留最常用的选择器，减少尝试时间
            show_more_selectors = [
                ".infinite-scroller__show-more-button",
                "//button[contains(text(), 'See more jobs')]"
            ]

            for selector in show_more_selectors:
                try:
                    if selector.startswith("//"):
                        button = self.driver.find_element(By.XPATH, selector)
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if button and button.is_displayed() and button.is_enabled():
                        logger.info(f"找到并点击显示更多按钮: {selector}")
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                        time.sleep(1)
                        button.click()
                        time.sleep(3)
                        return True
                except Exception:
                    continue

            return False
        except Exception as e:
            logger.debug(f"点击显示更多按钮时出错: {str(e)}")
            return False

    def _count_job_cards(self):
        """统计当前页面的职位卡片数量"""
        try:
            selectors = [
                ".base-search-card",
                "[data-job-id]",
                ".job-card-container",
                ".jobs-search-results__list-item"
            ]

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        return len(elements)
                except Exception:
                    continue

            return 0
        except Exception as e:
            logger.debug(f"统计职位卡片数量时出错: {str(e)}")
            return 0

    def _handle_pagination_and_collect_jobs(self, keywords: str = "", location: str = ""):
        """处理LinkedIn分页导航并收集所有页面的职位 - 优化版本"""
        try:
            # 🧹 清理开始：确保没有残留数据
            all_jobs = []

            current_page = 1
            max_pages = 20  # 增加最大页数，支持更多页面
            consecutive_failures = 0  # 连续失败计数
            max_consecutive_failures = 3  # 最大连续失败次数
            no_new_jobs_count = 0  # 连续无新职位计数
            max_no_new_jobs = 2  # 最大连续无新职位次数

            logger.info("🚀 开始处理LinkedIn分页导航（优化版本）...")
            logger.info(f"📊 配置: 最大页数={max_pages}, 最大连续失败={max_consecutive_failures}")
            logger.info(f"🔍 搜索条件: 关键词='{keywords}', 地点='{location}'")

            while current_page <= max_pages:
                logger.info(f"📄 正在处理第 {current_page} 页...")

                # 等待页面加载完成
                time.sleep(1)  # 减少等待时间从3秒到1秒

                # 🔍 先检测是否为最后一页（在滚动前检测）
                is_last_page = self._is_last_page()
                if is_last_page:
                    logger.info("🔚 检测到最后一页，将完整滚动此页后终止，避免抓取推荐职位区域。")

                # 🔄 优化的职位提取策略：优先使用准确选择器
                page_jobs = self._extract_jobs_optimized(keywords, location)
                
                # 🛑 如果是最后一页，处理完成后立即终止
                if is_last_page:
                    if page_jobs:
                        all_jobs.extend(self._deduplicate_jobs(page_jobs, all_jobs))
                    logger.info("✅ 最后一页处理完成，终止分页循环。")
                    break

                if page_jobs:
                    # 改进的去重策略
                    new_jobs = self._deduplicate_jobs(page_jobs, all_jobs)
                    all_jobs.extend(new_jobs)

                    # 检查是否真的有新职位
                    if len(new_jobs) > 0:
                        consecutive_failures = 0  # 重置失败计数
                        no_new_jobs_count = 0  # 重置无新职位计数
                        logger.info(f"✅ 第 {current_page} 页新增 {len(new_jobs)} 个职位，总计 {len(all_jobs)} 个")
                    else:
                        no_new_jobs_count += 1
                        logger.warning(f"⚠️ 第 {current_page} 页未找到新职位（连续无新职位 {no_new_jobs_count} 次）")

                        # 如果连续多次没有新职位，可能是重复页面或到了最后
                        if no_new_jobs_count >= max_no_new_jobs:
                            logger.info(f"🔚 连续 {no_new_jobs_count} 次无新职位，可能已到最后或重复页面")
                            break
                else:
                    consecutive_failures += 1
                    logger.warning(f"❌ 第 {current_page} 页未找到职位（连续失败 {consecutive_failures} 次）")

                # 如果连续多次没有找到任何职位，可能页面有问题
                if consecutive_failures >= max_consecutive_failures:
                    logger.info(f"🚨 连续 {consecutive_failures} 次未找到职位，停止分页处理")
                    break

                # 🔄 尝试翻到下一页（只有非最后一页才尝试）
                logger.info(f"🔄 尝试从第 {current_page} 页翻到下一页...")
                self._ensure_pagination_visible()
                next_page_success = self._click_next_page()
                if not next_page_success:
                    logger.info("🔚 无法点击下一页，可能已到最后一页，分页处理完成")
                    break

                current_page += 1

                # 额外安全检查：如果页面数超过预期，给出警告但继续
                if current_page > 15:
                    logger.warning(f"⚠️ 已处理 {current_page-1} 页，页数较多，请确认是否正常")

                if current_page > max_pages:
                    logger.warning(f"🛑 已达到最大页数限制 {max_pages}，强制停止")
                    break

            logger.info(f"🎉 分页处理完成！")
            logger.info(f"📊 总结: 处理了 {current_page-1} 页，收集到 {len(all_jobs)} 个职位")
            return all_jobs

        except Exception as e:
            logger.error(f"❌ 处理分页时出错: {str(e)}")
            return []

    def _extract_jobs_optimized(self, keywords: str = "", location: str = "") -> List[Dict]:
        """优化的职位提取方法：优先使用准确选择器，简化HTML处理"""
        try:
            logger.info("🔍 开始优化职位提取...")
            
            # 等待页面加载完成
            self._wait_for_jobs_to_load()
            
            # 额外等待懒加载内容完全加载
            logger.info("⏳ 等待懒加载职位内容完全加载...")
            time.sleep(3)  # 调整回3秒，确保懒加载充分完成
            
            # 改进的滚动策略：逐个触发职位卡片的懒加载
            logger.info("🔄 执行智能滚动以触发所有职位卡片的懒加载...")
            # 检查是否为最后一页，避免触发推荐职位
            is_last_page = self._is_last_page()
            self._trigger_lazy_loading_for_all_jobs(is_last_page)
            
            # 优先使用最准确的选择器：.scaffold-layout__list-item
            primary_selector = ".scaffold-layout__list-item"
            job_elements = self.driver.find_elements(By.CSS_SELECTOR, primary_selector)
            
            logger.info(f"✅ 使用主选择器 {primary_selector} 找到 {len(job_elements)} 个职位元素")
            
            if len(job_elements) == 0:
                # 回退到其他选择器
                fallback_selectors = [
                    ".job-card-container",
                    ".jobs-search-results__list-item", 
                    ".job-card-job-posting-card-wrapper"
                ]
                
                for selector in fallback_selectors:
                    job_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if len(job_elements) > 0:
                        logger.info(f"✅ 回退选择器 {selector} 找到 {len(job_elements)} 个职位元素")
                        break
            
            if len(job_elements) == 0:
                logger.warning("❌ 未找到任何职位元素")
                return []
            
            # 提取职位卡片HTML并转换为JSON格式
            job_cards_html = []
            for i, element in enumerate(job_elements):
                try:
                    # 获取职位卡片的HTML
                    card_html = element.get_attribute('outerHTML')
                    if card_html and len(card_html.strip()) > 100:  # 🔧 降低最小长度要求
                        # 🔧 临时禁用质量检查，确保所有职位都能通过
                        job_cards_html.append({
                            'index': i + 1,
                            'html': card_html
                        })
                        logger.debug(f"第 {i+1} 个职位卡片HTML已提取，长度: {len(card_html)}")
                    else:
                        logger.warning(f"第 {i+1} 个职位卡片HTML内容太少，跳过")
                except Exception as e:
                    logger.debug(f"获取第 {i+1} 个职位卡片HTML失败: {e}")
                    continue
            
            if not job_cards_html:
                logger.warning("❌ 未能提取任何职位卡片HTML")
                return []
            
            logger.info(f"📄 成功提取 {len(job_cards_html)} 个职位卡片HTML")

            # 🔧 修复：为每页创建独立的HTML文件，避免覆盖
            try:
                # 检测当前页码
                current_url = self.driver.current_url
                page_num = 1
                if "start=" in current_url:
                    import re
                    match = re.search(r'start=(\d+)', current_url)
                    if match:
                        start_num = int(match.group(1))
                        page_num = (start_num // 25) + 1

                filename = f"job_cards_preview_page_{page_num}.html"
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(f"<html><head><meta charset='utf-8'><title>职位卡片预览 - 第{page_num}页</title></head><body>")
                    f.write(f"<h1>第 {page_num} 页职位卡片 ({len(job_cards_html)} 个)</h1>")
                    for card in job_cards_html:
                        f.write(f"<h2>职位卡片序号: {card['index']}</h2>\n{card['html']}<hr/>")
                    f.write("</body></html>")
                logger.info(f"已将第{page_num}页职位卡片HTML输出到 {filename} 文件。")
            except Exception as e:
                logger.error(f"写入职位卡片HTML文件失败: {e}")
            
            # 使用LLM解析职位信息（传入JSON格式的HTML数据）
            jobs = self._parse_job_cards_with_llm(job_cards_html, keywords, location)

            # 🔧 安全检查：只有当jobs不为空时才进行智能过滤
            if jobs and len(jobs) > 0:
                # 使用智能过滤区分主要职位和推荐职位
                filtered_jobs = self._filter_jobs_with_llm(jobs)
            else:
                logger.warning("⚠️ LLM解析返回空结果，跳过智能过滤")
                filtered_jobs = jobs or []
            
            logger.info(f"✅ 优化提取完成，获得 {len(filtered_jobs)} 个有效职位")
            return filtered_jobs
            
        except Exception as e:
            logger.error(f"❌ 优化职位提取失败: {e}")
            return []
    
    def _parse_job_cards_with_llm(self, job_cards_html: List[Dict], keywords: str = "", location: str = "") -> List[Dict]:
        """🔧 简化版：直接让LLM读取HTML，无需JSON转换"""
        try:
            import json

            # 🔧 智能处理HTML - 动态调整长度避免API超限
            combined_html = ""
            total_length = 0
            # 🔧 提高长度限制，基于实际数据需求优化
            max_length = 45000  # 提高到4.5万字符，支持第一页完整数据处理
            processed_cards = 0

            for i, card_data in enumerate(job_cards_html):
                card_html = card_data['html']
                # 简化HTML，只保留关键信息
                simplified_html = self._simplify_job_card_html(card_html)

                # 🔧 预估加上提示词后的总长度
                estimated_total = total_length + len(simplified_html) + 3000  # 为提示词预留3K字符

                if estimated_total > max_length:
                    logger.warning(f"⚠️ 预估总长度 {estimated_total} 超限，只处理前 {i} 个职位卡片")
                    break

                combined_html += f"\n<!-- 职位卡片 {i+1} -->\n{simplified_html}\n"
                total_length += len(simplified_html)
                processed_cards = i + 1

            logger.info(f"📊 处理了 {processed_cards}/{len(job_cards_html)} 个职位卡片，总HTML长度: {total_length} 字符")

            # 🔧 检查是否需要分批处理
            if processed_cards < len(job_cards_html):
                logger.warning(f"⚠️ 由于长度限制，将分批处理职位卡片 ({processed_cards}/{len(job_cards_html)})")

                # 🔧 智能分批：确保每批都在安全范围内
                batch_size = max(10, processed_cards)  # 至少10个职位一批
                all_results = []

                for batch_start in range(0, len(job_cards_html), batch_size):
                    batch_end = min(batch_start + batch_size, len(job_cards_html))
                    batch_cards = job_cards_html[batch_start:batch_end]

                    logger.info(f"🔄 处理第 {batch_start//batch_size + 1} 批职位卡片 ({batch_start+1}-{batch_end})...")
                    batch_results = self._process_job_cards_batch(batch_cards, keywords, location)

                    if batch_results:
                        all_results.extend(batch_results)
                        logger.info(f"✅ 第 {batch_start//batch_size + 1} 批处理完成，获得 {len(batch_results)} 个职位")
                    else:
                        logger.warning(f"⚠️ 第 {batch_start//batch_size + 1} 批处理失败")

                logger.info(f"🎉 分批处理完成，总共获得 {len(all_results)} 个职位")
                return all_results

            # 如果没有有效的HTML内容，返回空列表
            if not combined_html.strip():
                logger.error("❌ 没有有效的HTML内容可供解析")
                return []

            # 🔧 优化提示词 - 强调正确的URL提取
            prompt = f"""你是LinkedIn职位信息提取专家。请仔细分析HTML内容，提取**所有**职位信息。

⚠️ 重要：HTML中包含 {processed_cards} 个职位卡片，你必须提取所有职位，不要遗漏任何一个！

搜索条件：关键词={keywords}, 地点={location}

🔧 URL提取规则（非常重要）：
1. 查找 data-job-id="数字" 属性，这是真正的职位ID
2. 构建URL格式：https://www.linkedin.com/jobs/view/职位ID
3. 不要使用href中的搜索页面链接！

提取要求：
1. 仔细查找每个职位卡片的信息
2. 提取所有6个必需字段：title, company, location, url, job_id, is_easy_apply
3. 确保提取的职位数量等于输入的职位卡片数量

HTML内容：
{combined_html}

输出格式：只返回JSON数组，不要任何解释或额外文字
[{{"title":"职位标题","company":"公司名","location":"地点","url":"https://www.linkedin.com/jobs/view/4221974684","job_id":"4221974684","is_easy_apply":true}}]

关键要求：
- 必须提取所有 {processed_cards} 个职位
- url必须是 https://www.linkedin.com/jobs/view/职位ID 格式
- job_id从 data-job-id 属性提取
- is_easy_apply字段必须是布尔值true/false
- 不要返回任何解释、分析或其他文字，只要纯JSON数组"""

            # 🔧 直接调用Gemini API，增加重试机制
            response = self._call_gemini_api_with_retry(prompt, max_retries=2)
            if response:
                logger.info(f"✅ Gemini API调用成功，响应长度: {len(response)} 字符")

                # 🔧 智能提取JSON - 处理Gemini可能返回的额外内容
                json_content = self._extract_json_from_response(response)
                if json_content:
                    try:
                        jobs_data = json.loads(json_content)
                        if isinstance(jobs_data, list):
                            logger.info(f"✅ 成功解析 {len(jobs_data)} 个职位")
                            return jobs_data
                        else:
                            logger.error(f"❌ 返回格式错误: {type(jobs_data)}")
                            return []
                    except json.JSONDecodeError as e:
                        logger.error(f"❌ JSON解析失败: {e}")
                        logger.debug(f"JSON内容: {json_content[:1000]}")
                        return []
                else:
                    logger.error("❌ 未能从响应中提取有效JSON")
                    logger.debug(f"原始响应前1000字符: {response[:1000]}")
                    return []
            else:
                logger.error("❌ Gemini API返回空响应")
                logger.error(f"🔍 调试信息 - 输入数据大小: {len(combined_html)} 字符")
                logger.error(f"🔍 调试信息 - 关键词: {keywords}")
                logger.error(f"🔍 调试信息 - 地点: {location}")
                # 保存输入数据用于调试
                debug_file = f"debug_gemini_input_{int(time.time())}.txt"
                try:
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(f"Keywords: {keywords}\n")
                        f.write(f"Location: {location}\n")
                        f.write(f"HTML Length: {len(combined_html)}\n")
                        f.write(f"HTML Content:\n{combined_html[:5000]}")
                    logger.error(f"🔍 输入数据已保存到: {debug_file}")
                except Exception as e:
                    logger.error(f"保存调试文件失败: {e}")

                # 🔧 当LLM返回空响应时，尝试简单的HTML解析作为备用
                logger.info("🔄 尝试使用备用HTML解析方法...")
                return self._fallback_html_parsing(job_cards_html)
            
        except Exception as e:
            logger.error(f"❌ LLM职位卡片解析失败: {e}")
            # 🔧 LLM解析失败时，尝试备用方法
            logger.info("🔄 LLM解析失败，尝试备用HTML解析...")
            return self._fallback_html_parsing(job_cards_html)

    def _process_job_cards_batch(self, job_cards_html: List[Dict], keywords: str = "", location: str = "") -> List[Dict]:
        """🔧 分批处理职位卡片，避免API长度限制"""
        try:
            # 🔧 更保守的长度控制
            combined_html = ""
            total_length = 0
            max_batch_length = 25000  # 批次处理时使用更小的限制
            processed_in_batch = 0

            for i, card_data in enumerate(job_cards_html):
                card_html = card_data['html']
                simplified_html = self._simplify_job_card_html(card_html)

                # 检查是否会超过批次限制
                if total_length + len(simplified_html) + 2000 > max_batch_length and i > 0:
                    logger.warning(f"⚠️ 批次长度限制，只处理前 {i} 个职位")
                    break

                combined_html += f"\n<!-- 职位卡片 {i+1} -->\n{simplified_html}\n"
                total_length += len(simplified_html)
                processed_in_batch = i + 1

            if not combined_html.strip():
                logger.warning("⚠️ 批次中没有有效HTML内容")
                return []

            logger.info(f"📊 批次处理 {processed_in_batch}/{len(job_cards_html)} 个职位，HTML长度: {total_length} 字符")

            # 🔧 简化的提示词，减少长度
            prompt = f"""提取LinkedIn职位信息，返回JSON数组。

职位数量: {processed_in_batch}
搜索: {keywords} in {location}

URL规则: 使用data-job-id构建 https://www.linkedin.com/jobs/view/职位ID

HTML:
{combined_html}

返回JSON数组格式。"""

            # 调用API
            response = self._call_gemini_api_with_retry(prompt, max_retries=1)
            if response:
                json_content = self._extract_json_from_response(response)
                if json_content:
                    import json
                    jobs = json.loads(json_content)
                    logger.info(f"✅ 批次解析成功，提取到 {len(jobs)} 个职位")
                    return jobs

            # 失败时使用备用解析
            logger.warning("⚠️ 批次LLM解析失败，使用备用方法")
            return self._fallback_html_parsing(job_cards_html[:processed_in_batch])

        except Exception as e:
            logger.error(f"❌ 批次处理失败: {e}")
            return self._fallback_html_parsing(job_cards_html)

    def _fallback_html_parsing(self, job_cards_html: List[Dict]) -> List[Dict]:
        """🔧 备用HTML解析方法 - 当LLM失败时使用简单的正则表达式提取"""
        logger.info(f"🔄 开始备用HTML解析，处理 {len(job_cards_html)} 个职位卡片")

        jobs = []
        import re

        for i, card_data in enumerate(job_cards_html):
            try:
                # 🔧 从字典中提取HTML内容
                html = card_data.get('html', '') if isinstance(card_data, dict) else str(card_data)

                job = {}

                # 提取职位标题
                title_match = re.search(r'<h3[^>]*class="[^"]*job-title[^"]*"[^>]*>.*?<a[^>]*>([^<]+)</a>', html, re.IGNORECASE | re.DOTALL)
                if not title_match:
                    title_match = re.search(r'<a[^>]*class="[^"]*job-title[^"]*"[^>]*>([^<]+)</a>', html, re.IGNORECASE | re.DOTALL)
                job['title'] = title_match.group(1).strip() if title_match else f"职位 {i+1}"

                # 提取公司名称
                company_match = re.search(r'<h4[^>]*class="[^"]*company[^"]*"[^>]*>.*?<a[^>]*>([^<]+)</a>', html, re.IGNORECASE | re.DOTALL)
                if not company_match:
                    company_match = re.search(r'<a[^>]*class="[^"]*company[^"]*"[^>]*>([^<]+)</a>', html, re.IGNORECASE | re.DOTALL)
                job['company'] = company_match.group(1).strip() if company_match else "未知公司"

                # 提取地点
                location_match = re.search(r'<div[^>]*class="[^"]*job-location[^"]*"[^>]*>([^<]+)</div>', html, re.IGNORECASE | re.DOTALL)
                job['location'] = location_match.group(1).strip() if location_match else "未知地点"

                # 🔧 提取职位ID和构建URL - 从data-job-id属性提取
                job_id_match = re.search(r'data-job-id="(\d+)"', html, re.IGNORECASE)
                if job_id_match:
                    job_id = job_id_match.group(1)
                    job['job_id'] = job_id
                    job['url'] = f"https://www.linkedin.com/jobs/view/{job_id}"
                else:
                    # 备用方案：从currentJobId参数提取
                    current_job_id_match = re.search(r'currentJobId=(\d+)', html, re.IGNORECASE)
                    if current_job_id_match:
                        job_id = current_job_id_match.group(1)
                        job['job_id'] = job_id
                        job['url'] = f"https://www.linkedin.com/jobs/view/{job_id}"
                    else:
                        job['job_id'] = f"fallback_{i+1}"
                        job['url'] = f"https://www.linkedin.com/jobs/view/{job['job_id']}"

                # 检查Easy Apply - 🔧 匹配JobInfo模型字段名
                job['is_easy_apply'] = 'easy apply' in html.lower()  # 🔧 改为 is_easy_apply

                jobs.append(job)
                logger.debug(f"备用解析职位 {i+1}: {job['title']} @ {job['company']}")

            except Exception as e:
                logger.warning(f"备用解析第 {i+1} 个职位失败: {e}")
                continue

        logger.info(f"✅ 备用HTML解析完成，提取到 {len(jobs)} 个职位")
        return jobs

    def _find_json_end_position(self, json_str: str) -> int:
        """查找JSON字符串的结束位置，处理Extra data错误"""
        try:
            bracket_count = 0
            brace_count = 0
            in_string = False
            escape_next = False

            for i, char in enumerate(json_str):
                if escape_next:
                    escape_next = False
                    continue

                if char == '\\':
                    escape_next = True
                    continue

                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue

                if in_string:
                    continue

                if char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                elif char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1

                # 如果所有括号都匹配了，说明JSON结束了
                if bracket_count == 0 and brace_count == 0 and i > 0:
                    return i + 1

            return -1
        except Exception:
            return -1

    def _validate_job_card_html(self, html_content: str) -> bool:
        """验证职位卡片HTML是否包含足够的信息"""
        try:
            # 🔧 放宽验证条件：只检查基本的HTML结构
            basic_indicators = [
                'class=',     # 基本的CSS类
                'data-',      # 数据属性
                'href=',      # 链接
                'span',       # 基本HTML标签
                'div',        # 基本HTML标签
            ]

            # 至少要包含其中3个基本指标
            found_indicators = sum(1 for indicator in basic_indicators if indicator in html_content.lower())

            # 降低最小长度要求
            min_length = 200  # 降低最小HTML长度

            is_valid = found_indicators >= 3 and len(html_content) >= min_length

            if not is_valid:
                logger.debug(f"HTML验证失败: 基本指标数={found_indicators}/5, 长度={len(html_content)}")
                logger.debug(f"HTML内容前200字符: {html_content[:200]}")

            return is_valid

        except Exception as e:
            logger.debug(f"HTML验证出错: {e}")
            return True  # 出错时默认通过

    def _final_llm_filter_all_jobs(self, all_jobs: List[Dict], keywords: str = "", location: str = "") -> List[Dict]:
        """最终LLM汇总筛选：对所有页面收集的职位进行智能筛选和排序"""
        try:
            if not all_jobs:
                logger.warning("⚠️ 没有职位需要筛选")
                return []

            logger.info(f"🤖 开始对 {len(all_jobs)} 个职位进行最终LLM筛选...")

            import json

            # 构建最终筛选提示词
            prompt = f"""
你是一个专业的LinkedIn职位筛选和排序专家。请对以下职位列表进行智能筛选和排序。

搜索条件：
- 关键词: {keywords}
- 地点: {location}

🔧 重要筛选规则：
1. **地理位置保护规则**：
   - 不同城市/地点的职位必须分别保留，即使公司名和职位名相同
   - 只有当公司+职位名+地点完全相同时才视为重复职位
   - 例如：同一公司的"软件工程师"职位在"北京"和"上海"应该都保留

2. **去重标准**：
   - 相同公司 + 相同职位 + 相同地点 = 重复（删除）
   - 相同公司 + 相同职位 + 不同地点 = 不同机会（保留）
   - 不同公司 + 相同职位 + 任意地点 = 不同机会（保留）

3. **筛选标准**（按优先级排序，但不删除符合条件的职位）：
   - 职位标题与关键词的匹配度
   - 地点匹配度（优先排序，但不删除其他地点）
   - Easy Apply优先级
   - 公司知名度和规模

4. **质量筛选**：
   - 移除明显的垃圾职位或信息严重不完整的职位
   - 移除与搜索关键词完全无关的职位
   - 保留所有相关的职位机会，优先排序而非删除

职位列表：
{json.dumps(all_jobs, ensure_ascii=False, indent=2)}

请严格按照以下格式返回筛选后的职位列表，必须是有效的JSON数组格式，不要包含任何其他文本、解释或代码块标记：

[
  {{
    "title": "职位标题",
    "company": "公司名称",
    "location": "地点",
    "url": "职位链接",
    "is_easy_apply": true,
    "job_id": "职位ID"
  }}
]

确保返回的是纯JSON数组，不要添加任何前缀、后缀或解释文字。
"""

            # 获取API密钥
            api_key = self._get_api_key()
            if not api_key:
                logger.error("未找到API密钥，无法使用LLM筛选")
                return all_jobs

            # 导入LLM相关模块
            from langchain_google_genai import ChatGoogleGenerativeAI
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser

            # 初始化LLM
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.5-flash-preview-05-20",
                google_api_key=api_key,
                temperature=1.0,
                request_timeout=120.0,
                max_retries=3,
                transport="rest"
            )

            # 创建简单的提示模板（不需要变量替换）
            prompt_template = ChatPromptTemplate.from_template("{prompt}")

            # 创建链式处理
            chain = prompt_template | llm | StrOutputParser()

            # 调用LLM进行筛选
            logger.info("🤖 正在调用LLM进行最终职位筛选...")
            filtered_jobs = self._call_llm_with_retry(chain, {"prompt": prompt})

            # 添加调试信息
            logger.info(f"📋 LLM响应长度: {len(filtered_jobs) if filtered_jobs else 0}")
            if filtered_jobs:
                logger.info(f"📋 LLM响应前500字符: {filtered_jobs[:500]}")

            if filtered_jobs and filtered_jobs.strip():
                try:
                    # 尝试清理响应内容
                    cleaned_response = filtered_jobs.strip()

                    # 如果响应被包装在代码块中，提取JSON部分
                    if cleaned_response.startswith('```'):
                        # 查找JSON内容
                        lines = cleaned_response.split('\n')
                        json_lines = []
                        in_json = False
                        for line in lines:
                            if line.strip().startswith('[') or line.strip().startswith('{'):
                                in_json = True
                            if in_json:
                                json_lines.append(line)
                            if in_json and (line.strip().endswith(']') or line.strip().endswith('}')):
                                break
                        cleaned_response = '\n'.join(json_lines)

                    # 🔧 增强JSON解析容错性 - 处理截断的JSON
                    try:
                        result = json.loads(cleaned_response)
                        logger.info(f"✅ LLM筛选完成，从 {len(all_jobs)} 个职位筛选出 {len(result)} 个")
                        return result
                    except json.JSONDecodeError as e:
                        logger.warning(f"⚠️ JSON解析失败，尝试修复截断的JSON: {e}")

                        # 尝试修复截断的JSON数组
                        if cleaned_response.strip().startswith('[') and not cleaned_response.strip().endswith(']'):
                            # 查找最后一个完整的对象
                            last_complete_brace = cleaned_response.rfind('}')
                            if last_complete_brace > 0:
                                # 截取到最后一个完整对象，然后添加数组结束符
                                fixed_json = cleaned_response[:last_complete_brace + 1] + '\n]'
                                try:
                                    result = json.loads(fixed_json)
                                    logger.info(f"✅ JSON修复成功！从 {len(all_jobs)} 个职位筛选出 {len(result)} 个")
                                    return result
                                except json.JSONDecodeError:
                                    logger.warning("⚠️ JSON修复失败，返回原始职位列表")

                        # 如果修复失败，返回原始列表
                        logger.error(f"❌ 原始响应内容: {repr(filtered_jobs[:1000])}")
                        return all_jobs

                except json.JSONDecodeError as e:
                    logger.error(f"❌ LLM筛选结果JSON解析失败: {e}")
                    logger.error(f"❌ 原始响应内容: {repr(filtered_jobs[:1000])}")
                    return all_jobs
            else:
                logger.warning("⚠️ LLM筛选返回空响应，返回原始列表")
                return all_jobs

        except Exception as e:
            logger.error(f"❌ 最终LLM筛选失败: {e}")
            return all_jobs

    def _find_json_end_position(self, json_str: str) -> int:
        """查找JSON字符串的结束位置，处理Extra data错误"""
        try:
            bracket_count = 0
            brace_count = 0
            in_string = False
            escape_next = False

            for i, char in enumerate(json_str):
                if escape_next:
                    escape_next = False
                    continue

                if char == '\\':
                    escape_next = True
                    continue

                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue

                if in_string:
                    continue

                if char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                elif char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1

                # 如果所有括号都匹配了，说明JSON结束了
                if bracket_count == 0 and brace_count == 0 and i > 0:
                    return i + 1

            return -1
        except Exception:
            return -1

    def _validate_job_card_html(self, html_content: str) -> bool:
        """验证职位卡片HTML是否包含足够的信息"""
        try:
            # 🔧 放宽验证条件：只检查基本的HTML结构
            basic_indicators = [
                'class=',     # 基本的CSS类
                'data-',      # 数据属性
                'href=',      # 链接
                'span',       # 基本HTML标签
                'div',        # 基本HTML标签
            ]

            # 至少要包含其中3个基本指标
            found_indicators = sum(1 for indicator in basic_indicators if indicator in html_content.lower())

            # 降低最小长度要求
            min_length = 200  # 降低最小HTML长度

            is_valid = found_indicators >= 3 and len(html_content) >= min_length

            if not is_valid:
                logger.debug(f"HTML验证失败: 基本指标数={found_indicators}/5, 长度={len(html_content)}")
                logger.debug(f"HTML内容前200字符: {html_content[:200]}")

            return is_valid

        except Exception as e:
            logger.debug(f"HTML验证出错: {e}")
            return True  # 出错时默认通过




    def _filter_jobs_with_llm(self, jobs: List[Dict]) -> List[Dict]:
        """使用LLM智能过滤职位，区分主要职位和推荐职位"""
        try:
            if not jobs:
                return []
            
            # 首先使用基于规则的过滤
            filtered_jobs = self._filter_jobs_by_rules(jobs)
            
            # 如果规则过滤后职位数量合理，直接返回
            if len(filtered_jobs) <= 25:  # 基于25个职位/页的规律
                return filtered_jobs
            
            # 如果职位数量过多，使用LLM进行智能过滤
            logger.info(f"🤖 职位数量 {len(filtered_jobs)} 超过预期，使用LLM智能过滤...")
            
            import json
            prompt = f"""
你是LinkedIn职位过滤专家。请帮助识别并过滤掉推荐职位，只保留主要搜索结果。

判断标准：
1. 推荐职位通常包含"Promoted"、"Sponsored"等标记
2. 推荐职位可能与搜索条件相关性较低
3. 主要职位通常按相关性排序，推荐职位可能穿插其中
4. 每页主要职位通常不超过25个

职位数据：
{json.dumps(filtered_jobs, ensure_ascii=False, indent=2)}

请返回JSON格式，只包含主要职位（非推荐职位）。格式：
{{
  "main_jobs": [...],
  "filtered_count": 数量,
  "reason": "过滤原因"
}}
"""
            
            response = self._call_llm_api(prompt, json.dumps(filtered_jobs, ensure_ascii=False))
            
            if response:
                try:
                    result = json.loads(response)
                    if 'main_jobs' in result:
                        main_jobs = result['main_jobs']
                        logger.info(f"✅ LLM智能过滤完成，保留 {len(main_jobs)} 个主要职位")
                        return main_jobs
                except json.JSONDecodeError as e:
                    logger.error(f"❌ LLM过滤响应解析失败: {e}")
            
            # LLM过滤失败，返回规则过滤结果
            logger.warning("⚠️ LLM智能过滤失败，返回规则过滤结果")
            return filtered_jobs
            
        except Exception as e:
            logger.error(f"❌ LLM智能过滤失败: {e}")
            return jobs
    
    def _filter_jobs_by_rules(self, jobs: List[Dict]) -> List[Dict]:
        """基于规则的职位过滤"""
        filtered_jobs = []
        
        for job in jobs:
            # 跳过明确标记为推广的职位
            if job.get('promoted', False):
                logger.debug(f"跳过推广职位: {job.get('title', 'Unknown')}")
                continue
            
            # 🔧 安全检查职位标题和公司名称中的推荐关键词
            title = (job.get('title') or '').lower()
            company = (job.get('company') or '').lower()
            
            skip_keywords = ['sponsored', 'promoted', 'advertisement', 'ad']
            if any(keyword in title or keyword in company for keyword in skip_keywords):
                logger.debug(f"跳过包含推荐关键词的职位: {job.get('title', 'Unknown')}")
                continue
            
            filtered_jobs.append(job)
        
        return filtered_jobs
    
    def _deduplicate_jobs(self, new_jobs: List[Dict], existing_jobs: List[Dict]) -> List[Dict]:
        """改进的职位去重策略"""
        if not new_jobs:
            return []
        
        # 构建现有职位的签名集合
        existing_signatures = set()
        for job in existing_jobs:
            signature = self._generate_job_signature(job)
            existing_signatures.add(signature)
        
        # 过滤新职位
        unique_jobs = []
        for job in new_jobs:
            signature = self._generate_job_signature(job)
            if signature not in existing_signatures:
                existing_signatures.add(signature)
                unique_jobs.append(job)
            else:
                logger.debug(f"跳过重复职位: {job.get('title', 'Unknown')} @ {job.get('company', 'Unknown')} - {job.get('location', 'Unknown')}")
        
        return unique_jobs
    
    def _generate_job_signature(self, job: Dict) -> str:
        """
        生成职位签名用于去重
        🔧 修复：确保不同地点的相同职位不会被错误去重
        """
        title = (job.get('title') or '').strip().lower()
        company = (job.get('company') or '').strip().lower()
        location = (job.get('location') or '').strip().lower()
        job_id = (job.get('job_id') or '').strip()

        # 优先使用job_id，如果没有则使用组合签名
        if job_id:
            return f"id:{job_id}"
        else:
            # 🔧 关键修复：必须包含location，确保不同地点的职位不被去重
            # 格式：title|company|location - 三个字段都必须相同才视为重复
            return f"{title}|{company}|{location}"
    
    def _call_gemini_api_direct(self, prompt: str) -> str:
        """🔧 直接调用Gemini API，无需复杂的模板处理"""
        try:
            # 获取API密钥
            api_key = self._get_api_key()
            if not api_key:
                logger.error("未找到API密钥，无法调用Gemini API")
                return ""

            # 导入Gemini相关模块
            from langchain_google_genai import ChatGoogleGenerativeAI

            # 初始化Gemini
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.5-flash-preview-05-20",
                google_api_key=api_key,
                temperature=1.0,
                request_timeout=120.0,
                max_retries=3,
                transport="rest"
            )

            # 🔧 直接调用Gemini，增加重试和错误处理
            import time
            start_time = time.time()
            logger.info("🤖 正在调用Gemini API...")

            # 检查输入长度
            if len(prompt) > 100000:
                logger.warning(f"⚠️ 输入长度 {len(prompt)} 字符可能过长，Gemini API可能拒绝处理")

            response = llm.invoke(prompt)

            # 记录实际调用时间
            actual_time = time.time() - start_time
            logger.info(f"⏱️ Gemini API实际调用时间: {actual_time:.2f} 秒")

            # 提取文本内容
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)

            # 🔧 检查响应是否为空或无效
            if not response_text or len(response_text.strip()) == 0:
                logger.error("❌ Gemini API返回空响应，可能是输入过长或API限制")
                return ""

            logger.info(f"✅ Gemini API调用成功，响应长度: {len(response_text)} 字符")
            return response_text

        except Exception as e:
            logger.error(f"❌ Gemini API调用失败: {str(e)}")
            return ""

    def _call_gemini_api_with_retry(self, prompt: str, max_retries: int = 2) -> str:
        """🔧 带重试机制的Gemini API调用"""
        import time

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"🔄 Gemini API重试第 {attempt} 次...")
                    time.sleep(2 * attempt)  # 递增延迟

                response = self._call_gemini_api_direct(prompt)

                if response and len(response.strip()) > 0:
                    return response
                else:
                    logger.warning(f"⚠️ 第 {attempt + 1} 次尝试返回空响应")
                    if attempt < max_retries:
                        # 尝试缩短提示词
                        if len(prompt) > 50000:
                            logger.info("🔧 尝试缩短提示词长度...")
                            # 简化提示词，保留核心部分
                            prompt = prompt[:50000] + "\n\n请返回JSON数组格式的职位信息。"

            except Exception as e:
                logger.error(f"❌ 第 {attempt + 1} 次API调用失败: {e}")
                if attempt < max_retries:
                    time.sleep(3 * attempt)

        logger.error("❌ 所有重试都失败了")
        return ""

    def _extract_json_from_response(self, response: str) -> str:
        """🔧 智能提取JSON内容，处理Gemini可能返回的额外文字"""
        try:
            response = response.strip()

            # 1. 寻找JSON数组开始位置
            json_start = -1
            for i, char in enumerate(response):
                if char == '[':
                    json_start = i
                    break

            if json_start == -1:
                logger.warning("❌ 未找到JSON数组开始标记 '['")
                return ""

            # 2. 寻找JSON数组结束位置（匹配括号）
            bracket_count = 0
            json_end = -1
            for i in range(json_start, len(response)):
                char = response[i]
                if char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                    if bracket_count == 0:
                        json_end = i + 1
                        break

            if json_end == -1:
                logger.warning("❌ 未找到JSON数组结束标记 ']'")
                return ""

            # 3. 提取JSON内容
            json_content = response[json_start:json_end]
            logger.info(f"✅ 成功提取JSON，长度: {len(json_content)} 字符")
            logger.debug(f"JSON前100字符: {json_content[:100]}")
            logger.debug(f"JSON后100字符: {json_content[-100:]}")

            return json_content

        except Exception as e:
            logger.error(f"❌ JSON提取失败: {e}")
            return ""

    def _simplify_job_card_html(self, html: str) -> str:
        """🔧 简化职位卡片HTML，只保留关键信息"""
        try:
            import re

            # 🔧 移除不必要的属性和样式，但保留关键的data-job-id
            html = re.sub(r'\s+class="[^"]*"', '', html)
            html = re.sub(r'\s+id="[^"]*"', '', html)
            html = re.sub(r'\s+style="[^"]*"', '', html)
            # 保留data-job-id，移除其他data属性
            html = re.sub(r'\s+data-(?!job-id)[^=]*="[^"]*"', '', html)
            html = re.sub(r'\s+aria-[^=]*="[^"]*"', '', html)
            html = re.sub(r'\s+loading="[^"]*"', '', html)
            html = re.sub(r'\s+width="[^"]*"', '', html)
            html = re.sub(r'\s+height="[^"]*"', '', html)
            html = re.sub(r'\s+role="[^"]*"', '', html)
            html = re.sub(r'\s+tabindex="[^"]*"', '', html)

            # 移除图片和多余的标签
            html = re.sub(r'<img[^>]*>', '', html)
            html = re.sub(r'<svg[^>]*>.*?</svg>', '', html, flags=re.DOTALL)
            html = re.sub(r'<!--.*?-->', '', html, flags=re.DOTALL)

            # 压缩空白字符
            html = re.sub(r'\s+', ' ', html)
            html = html.strip()

            return html

        except Exception as e:
            logger.warning(f"⚠️ HTML简化失败: {e}")
            return html

    def _normalize_job_fields(self, jobs: List[Dict]) -> List[Dict]:
        """🔧 标准化职位字段名，确保匹配JobInfo模型"""
        normalized_jobs = []

        for job in jobs:
            if not isinstance(job, dict):
                continue

            normalized_job = job.copy()

            # 🔧 字段名转换映射
            field_mapping = {
                'job_url': 'url',           # job_url → url
                'easy_apply': 'is_easy_apply'  # easy_apply → is_easy_apply
            }

            # 执行字段名转换
            for old_field, new_field in field_mapping.items():
                if old_field in normalized_job:
                    normalized_job[new_field] = normalized_job.pop(old_field)

            # 🔧 确保必需字段存在
            required_fields = {
                'title': '未知职位',
                'company': '未知公司',
                'location': '未知地点',
                'url': 'https://www.linkedin.com/jobs/',
                'is_easy_apply': True,
                'job_id': f"auto_{hash(str(normalized_job))}"
            }

            for field, default_value in required_fields.items():
                if field not in normalized_job or not normalized_job[field]:
                    normalized_job[field] = default_value

            # 🔧 确保URL格式正确
            if not normalized_job['url'].startswith('http'):
                if normalized_job['url'].startswith('/jobs/view/'):
                    normalized_job['url'] = f"https://www.linkedin.com{normalized_job['url']}"
                elif normalized_job['job_id'] and normalized_job['job_id'] != f"auto_{hash(str(normalized_job))}":
                    normalized_job['url'] = f"https://www.linkedin.com/jobs/view/{normalized_job['job_id']}/"

            normalized_jobs.append(normalized_job)

        logger.info(f"🔧 字段标准化完成，处理了 {len(normalized_jobs)} 个职位")
        return normalized_jobs

    def _call_llm_api(self, prompt: str, html_content: str = "", **kwargs) -> str:
        """调用LLM API进行职位解析
        
        Args:
            prompt: LLM提示词
            html_content: 要解析的HTML内容（可选）
            **kwargs: 其他模板变量
            
        Returns:
            LLM响应结果
        """
        try:
            # 获取API密钥
            api_key = self._get_api_key()
            if not api_key:
                logger.error("未找到API密钥，无法使用LLM解析")
                return "[]"

            # 导入LLM相关模块
            from langchain_google_genai import ChatGoogleGenerativeAI
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser

            # 初始化LLM
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.5-flash-preview-05-20",
                google_api_key=api_key,
                temperature=1.0,
                request_timeout=120.0,  # 🔧 增加超时时间到2分钟
                max_retries=3,
                transport="rest"
            )

            # 创建提示模板和输入数据
            prompt_template = ChatPromptTemplate.from_template(prompt)
            input_data = {}
            
            # 处理HTML内容
            if html_content:
                # 🔧 调整HTML内容大小，与主要解析限制保持一致
                truncated_html = self._truncate_html_for_llm(html_content, max_chars=40000)  # 提高到4万字符
                input_data["html_content"] = truncated_html
                logger.info(f"📊 LLM输入HTML大小: {len(truncated_html)} 字符")
            
            # 添加其他变量
            input_data.update(kwargs)
            
            # 创建链式处理
            chain = prompt_template | llm | StrOutputParser()
            
            # 调用LLM（带重试机制）
            logger.info("🤖 正在调用LLM API进行职位解析...")
            response = self._call_llm_with_retry(chain, input_data)
            
            logger.info(f"✅ LLM API调用成功，响应长度: {len(response)} 字符")
            return response
            
        except Exception as e:
            logger.error(f"❌ LLM API调用失败: {str(e)}")
            return "[]"

    def _debug_all_next_buttons(self):
        """调试函数：列出页面上所有可能的Next按钮"""
        try:
            logger.info("🔍 调试：查找页面上所有可能的Next按钮...")

            # 查找所有包含"Next"的按钮
            all_next_selectors = [
                "//button[contains(text(), 'Next')]",
                "//button[contains(@aria-label, 'Next')]",
                "//button[contains(@aria-label, 'next')]",
                "//button[contains(@class, 'next')]",
                "//button[contains(@class, 'Next')]",
                "//a[contains(text(), 'Next')]",
                "//a[contains(@aria-label, 'Next')]"
            ]

            all_buttons = []
            for selector in all_next_selectors:
                try:
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    all_buttons.extend(buttons)
                except:
                    continue

            logger.info(f"🔍 找到 {len(all_buttons)} 个可能的Next按钮:")
            for i, btn in enumerate(all_buttons, 1):
                try:
                    btn_text = btn.text.strip()
                    btn_aria = btn.get_attribute('aria-label') or ''
                    btn_class = btn.get_attribute('class') or ''
                    btn_tag = btn.tag_name
                    btn_location = btn.location
                    logger.info(f"   {i}. {btn_tag} - text='{btn_text}', aria-label='{btn_aria}', location={btn_location}")
                    logger.info(f"      class='{btn_class[:100]}...'")
                except Exception as e:
                    logger.info(f"   {i}. 无法获取按钮信息: {e}")

        except Exception as e:
            logger.error(f"调试Next按钮时出错: {e}")

    def _click_next_page(self):
        """点击下一页按钮 - 修复逻辑，优先尝试点击而不是预判"""
        try:
            logger.info("🔍 开始寻找下一页按钮...")

            # 注释掉调试函数以提高性能
            # self._debug_all_next_buttons()

            # LinkedIn分页按钮选择器 - 优化后的高效选择器
            # 🎯 基于成功日志，优先使用最有效的选择器
            next_page_selectors = [
                # 1. 最有效的选择器 - 基于成功的日志
                "//button[contains(@class, 'jobs-search-pagination__button--next')]",
                ".jobs-search-pagination__button--next",

                # 2. 带aria-label的精确选择器
                "//button[@aria-label='View next page' and contains(@class, 'jobs-search-pagination__button--next')]",
                "button[aria-label='View next page'].jobs-search-pagination__button--next"
            ]

            found_next_button = False

            for i, selector in enumerate(next_page_selectors, 1):
                try:
                    logger.info(f"🔍 尝试选择器 {i}/{len(next_page_selectors)}: {selector}")

                    if selector.startswith("//"):
                        next_buttons = self.driver.find_elements(By.XPATH, selector)
                    else:
                        next_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for next_button in next_buttons:
                        if next_button and next_button.is_displayed():
                            found_next_button = True
                            logger.info(f"✅ 找到Next按钮: {selector}")

                            # 快速检查按钮状态
                            if not next_button.is_enabled():
                                logger.debug("❌ Next按钮已禁用，跳过此按钮")
                                continue

                            # 记录当前URL用于验证页面是否真的改变了
                            current_url = self.driver.current_url
                            logger.info(f"📍 当前URL: {current_url}")

                            logger.info(f"🎯 尝试点击Next按钮...")
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                            time.sleep(1)

                            try:
                                next_button.click()
                                logger.info("✅ 标准点击成功")
                            except Exception as e:
                                logger.info(f"⚠️ 标准点击失败，尝试JS点击: {str(e)}")
                                self.driver.execute_script("arguments[0].click();", next_button)
                                logger.info("✅ JS点击成功")

                            time.sleep(2)  # 减少等待时间从4秒到2秒

                            # 验证页面是否真的改变了
                            new_url = self.driver.current_url
                            logger.info(f"📍 新URL: {new_url}")

                            if current_url == new_url:
                                logger.warning("⚠️ 点击Next按钮后URL未改变，继续尝试其他按钮")
                                continue

                            logger.info("🎉 成功跳转到下一页！")
                            return True

                except Exception as e:
                    logger.debug(f"❌ 选择器 {selector} 失败: {str(e)}")
                    continue

            # 如果找到了Next按钮但都无法点击，说明可能到了最后一页
            if found_next_button:
                logger.info("🔚 找到了Next按钮但都无法点击，可能已到最后一页")
                return False

            # 如果没有找到Next按钮，尝试数字分页
            logger.info("🔢 未找到Next按钮，尝试数字分页...")
            logger.info("💡 这在多页搜索结果中很常见，如 '1 2 3 ... Next' 结构")
            return self._click_numbered_pagination()

        except Exception as e:
            logger.error(f"❌ 点击下一页按钮时出错: {str(e)}")
            return False

    def _is_last_page(self):
        """检测是否已经到了最后一页"""
        try:
            # 🎯 方法1: 检查Next按钮是否存在且可用
            next_button_selectors = [
                "button[aria-label='View next page'].jobs-search-pagination__button--next",
                "//button[@aria-label='View next page' and contains(@class, 'jobs-search-pagination__button--next')]",
                "//button[contains(@class, 'jobs-search-pagination__button--next')]",
                "//button[@aria-label='View next page']",
                "//button[contains(text(), 'Next')]",
                ".jobs-search-pagination__button--next"
            ]

            next_button_found = False
            for selector in next_button_selectors:
                try:
                    if selector.startswith("//"):
                        next_buttons = self.driver.find_elements(By.XPATH, selector)
                    else:
                        next_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for next_button in next_buttons:
                        if next_button.is_displayed():
                            next_button_found = True
                            button_class = next_button.get_attribute("class") or ""
                            button_disabled = next_button.get_attribute("disabled")
                            button_aria_disabled = next_button.get_attribute("aria-disabled")

                            # 检查按钮是否被禁用
                            if (button_disabled == "true" or
                                button_aria_disabled == "true" or
                                "disabled" in button_class.lower() or
                                not next_button.is_enabled()):
                                logger.info("✅ Next按钮已禁用，确认为最后一页")
                                return True
                            else:
                                logger.info("🔄 Next按钮可用，不是最后一页")
                                return False
                except Exception:
                    continue
            
            # 如果没有找到Next按钮，可能是最后一页
            if not next_button_found:
                logger.info("🔚 未找到Next按钮，可能是最后一页")
                return True

            # 方法2: 检查分页信息文本
            pagination_info_selectors = [
                ".artdeco-pagination__pages-state",
                "//span[contains(text(), 'of')]",
                "//span[contains(@class, 'pagination')]"
            ]

            for selector in pagination_info_selectors:
                try:
                    if selector.startswith("//"):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if element:
                        text = element.text.strip()
                        # 检查类似 "Page 2 of 2" 的文本
                        if "of" in text:
                            parts = text.split("of")
                            if len(parts) == 2:
                                try:
                                    current = int(parts[0].strip().split()[-1])
                                    total = int(parts[1].strip())
                                    if current >= total:
                                        logger.info(f"分页信息显示已到最后一页: {text}")
                                        return True
                                except ValueError:
                                    continue
                except Exception:
                    continue

            # 方法3: 检查是否只有一页（没有分页控件）
            pagination_selectors = [
                ".artdeco-pagination",
                "//nav[contains(@class, 'pagination')]",
                "//button[contains(@aria-label, 'Page')]"
            ]

            has_pagination = False
            for selector in pagination_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if elements:
                        has_pagination = True
                        break
                except Exception:
                    continue

            if not has_pagination:
                logger.info("未找到分页控件，可能只有一页")
                return True

            return False

        except Exception as e:
            logger.debug(f"检测最后一页时出错: {str(e)}")
            return False

    def _click_numbered_pagination(self):
        """点击数字分页按钮 - 优化检测逻辑"""
        try:
            logger.info("🔢 开始数字分页检测（限定在jobs-search-results-footer分页区域）...")

            # 1. 精确定位分页ul容器
            try:
                ul = self.driver.find_element(By.CSS_SELECTOR, "#jobs-search-results-footer > div.jobs-search-pagination.jobs-search-results-list__pagination.p4 > ul")
            except Exception as e:
                logger.warning(f"❌ 未找到分页ul容器: {str(e)}")
                return False

            # 2. 在ul下查找所有分页按钮（li > button）
            page_buttons = ul.find_elements(By.CSS_SELECTOR, "li > button")
            logger.info(f"📋 在分页ul下找到 {len(page_buttons)} 个分页按钮")
            for i, btn in enumerate(page_buttons):
                try:
                    btn_text = btn.text.strip()
                    btn_aria = btn.get_attribute('aria-label') or ''
                    btn_class = btn.get_attribute('class') or ''
                    logger.info(f"   按钮{i+1}: 文本='{btn_text}', aria-label='{btn_aria}', class='{btn_class}'")
                except:
                    pass

            if not page_buttons:
                logger.info("❌ 分页ul下未找到任何分页按钮")
                return False

            # 3. 找到当前激活的页码
            current_page_num = None
            for btn in page_buttons:
                try:
                    btn_text = btn.text.strip()
                    btn_class = btn.get_attribute('class') or ''
                    btn_aria_current = btn.get_attribute('aria-current')
                    if (btn_aria_current == 'true' or
                        'selected' in btn_class or
                        'active' in btn_class or
                        'artdeco-pagination__button--active' in btn_class):
                        if btn_text.isdigit():
                            current_page_num = int(btn_text)
                            logger.info(f"✅ 检测到当前页码: {current_page_num}")
                            break
                except Exception:
                    continue

            # 如果找不到当前页码，尝试从URL中获取
            if current_page_num is None:
                try:
                    current_url = self.driver.current_url
                    logger.info(f"📍 当前URL: {current_url}")
                    if "start=" in current_url:
                        start_param = current_url.split("start=")[1].split("&")[0]
                        start_value = int(start_param)
                        current_page_num = (start_value // 25) + 1
                        logger.info(f"📊 从URL获取当前页码: {current_page_num} (start={start_value})")
                    else:
                        current_page_num = 1
                        logger.info("📊 URL中无start参数，假设为第1页")
                except Exception as e:
                    logger.info(f"⚠️ 从URL获取页码失败: {str(e)}")
                    current_page_num = 1

            # 4. 点击下一个页码按钮
            if current_page_num:
                next_page_num = current_page_num + 1
                logger.info(f"🎯 当前页码: {current_page_num}，尝试点击页码: {next_page_num}")
                for btn in page_buttons:
                    try:
                        btn_text = btn.text.strip()
                        if btn_text.isdigit() and int(btn_text) == next_page_num:
                            if btn.is_displayed() and btn.is_enabled():
                                current_url = self.driver.current_url
                                logger.info(f"✅ 找到页码 {next_page_num} 按钮，准备点击")
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", btn)
                                time.sleep(1)
                                try:
                                    btn.click()
                                    logger.info("✅ 标准点击成功")
                                except Exception:
                                    self.driver.execute_script("arguments[0].click();", btn)
                                    logger.info("✅ JS点击成功")
                                time.sleep(4)
                                new_url = self.driver.current_url
                                logger.info(f"📍 新URL: {new_url}")
                                if current_url == new_url:
                                    logger.warning("⚠️ 点击页码按钮后URL未改变，继续尝试其他按钮")
                                    continue
                                logger.info(f"🎉 成功跳转到第 {next_page_num} 页！")
                                return True
                    except Exception as e:
                        logger.debug(f"❌ 尝试点击页码 {next_page_num} 失败: {str(e)}")
                        continue

            logger.info("🔚 未找到下一页按钮，可能已到最后一页")
            return False

        except Exception as e:
            logger.error(f"❌ 点击数字分页按钮时出错: {str(e)}")
            return False

    def _parse_jobs_with_llm(self, page_source: str, keywords: str = "", location: str = "") -> List[Dict]:
        """使用LLM解析职位信息"""
        try:
            logger.info("开始使用LLM解析职位信息...")

            # 🔧 简化：直接使用页面源码进行LLM解析
            html_to_parse = page_source

            # 获取API密钥
            api_key = self._get_api_key()
            if not api_key:
                logger.error("未找到API密钥，无法使用LLM解析")
                return []

            # 导入LLM相关模块
            from langchain_google_genai import ChatGoogleGenerativeAI
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            import json

            # 初始化LLM
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.5-flash-preview-05-20",
                google_api_key=api_key,
                temperature=1.0,  # 使用用户偏好的温度设置
                request_timeout=120.0,  # 🔧 增加超时时间到2分钟
                max_retries=3,
                transport="rest"
            )

            # 创建平衡的解析提示
            if keywords and location:
                prompt_template = ChatPromptTemplate.from_template("""
你是LinkedIn职位信息提取专家。请从HTML中提取所有职位信息，然后我会在后续步骤中进行关键词过滤。

参考搜索条件：
- 关键词: {keywords}
- 地点: {location}

提取要求：
1. 提取所有可见的职位信息，包括职位标题、公司名称、地点、链接等
2. **优先处理主要职位列表**：
   - 重点关注 <!-- MAIN_JOB_LIST_START --> 和 <!-- MAIN_JOB_LIST_END --> 标记之间的内容
   - 这些是与搜索条件最相关的主要职位结果
3. **谨慎处理推荐职位**：
   - <!-- RECOMMENDATION_SECTION_START --> 和 <!-- RECOMMENDATION_SECTION_END --> 标记之间的内容是推荐职位
   - 只有当推荐职位与搜索关键词高度相关时才包含
   - 推荐职位通常相关性较低，应优先选择主要职位列表中的结果
4. 确保提取的是真实的职位信息，不是页面导航或广告内容
5. 🔗 **重要：正确提取职位链接**
   - 寻找href="/jobs/view/数字ID"格式的链接
   - 如果是相对路径（如"/jobs/view/4255061508"），请保持原样，系统会自动补全
   - 确保提取的是职位标题对应的链接，不是其他链接
6. 从职位链接中提取job_id（通常在URL的/jobs/view/后面的数字）
7. 检查是否有"Easy Apply"按钮来判断is_easy_apply字段
8. 如果地点信息不完整，尽量补充城市或国家信息

HTML内容: {html_content}

请直接返回JSON数组，不要包含任何解释或代码块标记：
[{{"title":"职位标题","company":"公司名称","location":"工作地点","url":"完整LinkedIn链接","is_easy_apply":true,"job_id":"职位ID"}}]
""")
            else:
                # 回退到原始提示
                prompt_template = ChatPromptTemplate.from_template("""
从HTML提取职位信息，只返回JSON数组，不要包含代码或解释。

HTML: {html_content}

只返回JSON格式：[{{"title":"标题","company":"公司","location":"地点","url":"链接","is_easy_apply":true,"job_id":"ID"}}]
""")

            # 🔧 截取HTML内容的关键部分以避免token限制
            truncated_html = self._truncate_html_for_llm(html_to_parse, max_chars=40000)  # 提高到4万字符
            logger.info(f"📊 LLM输入HTML大小: {len(truncated_html)} 字符")

            # 创建链式处理
            chain = prompt_template | llm | StrOutputParser()

            # 调用LLM（带重试机制）
            logger.info(f"正在调用LLM解析职位信息... 关键词: {keywords}, 地点: {location}")
            if keywords and location:
                response = self._call_llm_with_retry(chain, {
                    "html_content": truncated_html,
                    "keywords": keywords,
                    "location": location
                })
            else:
                response = self._call_llm_with_retry(chain, {"html_content": truncated_html})

            # 添加调试信息
            logger.info(f"LLM原始响应长度: {len(response)} 字符")
            logger.info(f"LLM响应前500字符: {response[:500]}")

            # 解析JSON响应
            try:
                # 清理响应文本，移除可能的markdown标记和代码块
                clean_response = response.strip()

                # 移除各种代码块标记
                if clean_response.startswith("```python"):
                    clean_response = clean_response[9:]
                elif clean_response.startswith("```json"):
                    clean_response = clean_response[7:]
                elif clean_response.startswith("```"):
                    clean_response = clean_response[3:]

                if clean_response.endswith("```"):
                    clean_response = clean_response[:-3]

                # 如果响应包含Python代码，尝试提取JSON部分
                import re
                json_match = re.search(r'\[.*?\]', clean_response, re.DOTALL)
                if json_match:
                    clean_response = json_match.group()
                else:
                    # 如果没有找到JSON数组，查找单个JSON对象
                    json_match = re.search(r'\{.*?\}', clean_response, re.DOTALL)
                    if json_match:
                        clean_response = '[' + json_match.group() + ']'

                clean_response = clean_response.strip()
                logger.info(f"清理后的响应前500字符: {clean_response[:500]}")

                jobs_data = json.loads(clean_response)

                # 验证和清理数据
                validated_jobs = []
                seen_job_ids = set()

                for job in jobs_data:
                    if isinstance(job, dict) and job.get('title') and job.get('company'):
                        # 🔧 确保所有必需字段存在
                        if not job.get('job_id'):
                            job['job_id'] = self._extract_job_id(job.get('url', '')) or str(hash(job.get('title', '') + job.get('company', '')))

                        # 🔧 确保is_easy_apply字段存在
                        if 'is_easy_apply' not in job:
                            job['is_easy_apply'] = True  # 默认为True，假设支持Easy Apply

                        # 🔧 确保url字段存在
                        if not job.get('url'):
                            # 如果没有URL，尝试构建一个基本的LinkedIn职位URL
                            if job.get('job_id'):
                                job['url'] = f"https://www.linkedin.com/jobs/view/{job['job_id']}/"
                            else:
                                logger.warning(f"⚠️ 职位缺少URL且无法构建: {job.get('title', 'Unknown')}")
                                job['url'] = "https://www.linkedin.com/jobs/"  # 提供一个默认URL

                        # 🔗 修复URL处理逻辑 - 确保URL是完整的LinkedIn链接
                        if job.get('url'):
                            url = job['url'].strip()
                            if not url.startswith('http'):
                                if url.startswith('/'):
                                    # 相对路径，添加LinkedIn域名
                                    job['url'] = 'https://www.linkedin.com' + url
                                elif url.startswith('linkedin.com'):
                                    # 缺少协议的完整域名
                                    job['url'] = 'https://' + url
                                else:
                                    # 其他情况，尝试构建完整URL
                                    if 'jobs/view/' in url:
                                        job['url'] = 'https://www.linkedin.com/' + url.lstrip('/')
                                    else:
                                        logger.warning(f"⚠️ 无法识别的URL格式: {url}")

                            # 清理URL中的多余参数，保留核心职位链接
                            if 'linkedin.com/jobs/view/' in job['url']:
                                # 提取核心职位ID和基本参数
                                import re
                                match = re.search(r'/jobs/view/(\d+)', job['url'])
                                if match:
                                    job_id_from_url = match.group(1)
                                    # 构建干净的职位URL
                                    job['url'] = f"https://www.linkedin.com/jobs/view/{job_id_from_url}/"
                                    logger.debug(f"🔗 清理后的URL: {job['url']}")
                        else:
                            logger.warning(f"⚠️ 职位缺少URL: {job.get('title', 'Unknown')} @ {job.get('company', 'Unknown')}")

                        # 🔧 改进的去重策略：基于多个字段组合，安全处理None值
                        # 🎯 地理位置保护：确保不同地点的相同职位不被去重
                        job_id = job.get('job_id')
                        title = (job.get('title') or '').strip().lower()
                        company = (job.get('company') or '').strip().lower()
                        location = (job.get('location') or '').strip().lower()

                        # 创建更唯一的标识符 - 包含location确保地理位置差异被保留
                        # 格式：title|company|location - 三个字段都必须相同才视为重复
                        unique_key = f"{title}|{company}|{location}"

                        # 使用job_id和unique_key双重去重
                        if job_id and job_id not in seen_job_ids:
                            # 🔧 检查是否已有相同的title+company+location组合，安全处理None值
                            existing_keys = [f"{(j.get('title') or '').strip().lower()}|{(j.get('company') or '').strip().lower()}|{(j.get('location') or '').strip().lower()}" for j in validated_jobs]
                            if unique_key not in existing_keys:
                                seen_job_ids.add(job_id)
                                validated_jobs.append(job)
                            else:
                                logger.debug(f"跳过重复职位: {title} @ {company} - {location}")
                        elif not job_id:
                            # 🔧 如果没有job_id，基于内容去重，安全处理None值
                            existing_keys = [f"{(j.get('title') or '').strip().lower()}|{(j.get('company') or '').strip().lower()}|{(j.get('location') or '').strip().lower()}" for j in validated_jobs]
                            if unique_key not in existing_keys:
                                validated_jobs.append(job)
                            else:
                                logger.debug(f"跳过无job_id的重复职位: {title} @ {company} - {location}")

                logger.info(f"LLM成功解析出 {len(validated_jobs)} 个唯一职位")

                # 额外的关键词过滤步骤
                if keywords:
                    filtered_jobs = self._filter_jobs_by_keywords(validated_jobs, keywords, location)
                    logger.info(f"关键词过滤后保留 {len(filtered_jobs)} 个相关职位")
                    return filtered_jobs

                return validated_jobs

            except json.JSONDecodeError as e:
                logger.error(f"LLM响应JSON解析失败: {str(e)}")
                logger.debug(f"LLM原始响应: {response[:500]}...")
                return []

        except Exception as e:
            logger.error(f"LLM解析职位信息失败: {str(e)}")
            return []

    def _filter_jobs_by_keywords(self, jobs: List[Dict], keywords: str, location: str) -> List[Dict]:
        """根据关键词过滤职位，平衡相关性和包容性"""
        if not keywords:
            return jobs

        filtered_jobs = []
        keywords_lower = keywords.lower()
        location_lower = location.lower() if location else ""

        # 分解关键词
        keyword_parts = [kw.strip() for kw in keywords_lower.replace(',', ' ').split() if kw.strip()]

        logger.info(f"🔍 开始智能关键词过滤，搜索关键词: {keywords}, 地点: {location}")
        logger.info(f"📝 关键词分解: {keyword_parts}")

        for job in jobs:
            title = (job.get('title') or '').lower()
            company = (job.get('company') or '').lower()
            job_location = (job.get('location') or '').lower()

            # 计算相关性得分
            relevance_score = 0
            match_details = []

            # 1. 标题关键词匹配 (权重最高)
            title_matches = []
            for kw in keyword_parts:
                if kw in title:
                    title_matches.append(kw)
                    relevance_score += 10  # 每个关键词匹配得10分

            if title_matches:
                match_details.append(f"标题匹配: {title_matches}")

            # 2. 复合关键词特殊处理
            if len(keyword_parts) >= 2:
                # 对于"Marketing Manager"这样的复合词，如果都匹配给额外加分
                if all(kw in title for kw in keyword_parts):
                    relevance_score += 20  # 复合词完全匹配额外加分（提高权重）
                    match_details.append("复合词完全匹配")
                # 如果只匹配部分关键词，降低分数（避免Operation Manager匹配Marketing Manager）
                elif len([kw for kw in keyword_parts if kw in title]) == 1:
                    # 只有一个关键词匹配时，检查是否是核心业务词汇
                    matched_kw = [kw for kw in keyword_parts if kw in title][0]
                    if matched_kw in ['manager', 'director', 'lead', 'senior', 'specialist']:
                        relevance_score += 2  # 大幅降低仅职级匹配的分数
                        match_details.append(f"仅职级匹配: {matched_kw}")
                    else:
                        relevance_score += 5  # 业务词汇匹配给予适中分数
                        match_details.append(f"部分匹配: {matched_kw}")

            # 3. 相关词汇匹配 (扩展匹配)
            related_keywords = {
                'marketing': ['brand', 'digital', 'campaign', 'promotion', 'advertising', 'growth'],
                'manager': ['lead', 'director', 'head', 'supervisor', 'coordinator'],
                'developer': ['engineer', 'programmer', 'coder', 'dev'],
                'analyst': ['analysis', 'analytics', 'data', 'research'],
                'sales': ['business development', 'account', 'revenue', 'client']
            }

            for main_kw, related_list in related_keywords.items():
                if main_kw in keyword_parts:
                    for related in related_list:
                        if related in title:
                            relevance_score += 3  # 相关词汇匹配得3分
                            match_details.append(f"相关词: {related}")

            # 4. 地点匹配检查
            location_match = True
            if location_lower:
                location_parts = [loc.strip() for loc in location_lower.replace(',', ' ').split() if loc.strip()]
                location_match = any(loc in job_location for loc in location_parts)
                if location_match:
                    relevance_score += 5
                    match_details.append("地点匹配")

            # 5. 排除明显不相关的职位 (但更宽松)
            excluded_keywords = ['hr specialist', 'human resources specialist', 'recruiter', 'recruitment consultant']
            is_excluded = False

            # 只有当搜索关键词不包含这些词，且职位标题完全匹配排除词时才排除
            if not any(excl.replace(' ', '') in keywords_lower.replace(' ', '') for excl in excluded_keywords):
                is_excluded = any(excl in title for excl in excluded_keywords)
                if is_excluded:
                    relevance_score = 0  # 被排除的职位得分归零

            # 6. 决定是否保留职位 (提高精确度)
            min_score = 12  # 提高最低分数要求，确保更精确的匹配
            if relevance_score >= min_score and location_match and not is_excluded:
                job['relevance_score'] = relevance_score  # 添加相关性得分
                filtered_jobs.append(job)
                logger.debug(f"✅ 保留职位 (得分:{relevance_score}): {job.get('title')} @ {job.get('company')} - {', '.join(match_details)}")
            else:
                logger.debug(f"❌ 过滤掉职位 (得分:{relevance_score}): {job.get('title')} @ {job.get('company')} - 地点匹配:{location_match}, 被排除:{is_excluded}")

        # 按相关性得分排序
        filtered_jobs.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

        logger.info(f"🎯 智能过滤完成: {len(jobs)} -> {len(filtered_jobs)} 个职位")
        if filtered_jobs:
            logger.info(f"📊 得分范围: {filtered_jobs[-1].get('relevance_score', 0)} - {filtered_jobs[0].get('relevance_score', 0)}")

        return filtered_jobs

    def _call_llm_with_retry(self, chain, input_data: dict, max_retries: int = 3) -> str:
        """带重试机制的LLM调用，处理配额限制"""
        import time

        for attempt in range(max_retries):
            try:
                logger.info(f"LLM调用尝试 {attempt + 1}/{max_retries}")
                response = chain.invoke(input_data)
                logger.info("LLM调用成功")
                return response

            except Exception as e:
                error_str = str(e)
                logger.warning(f"LLM调用失败 (尝试 {attempt + 1}/{max_retries}): {error_str}")

                # 检查是否是配额限制错误
                if "429" in error_str or "quota" in error_str.lower() or "rate limit" in error_str.lower():
                    if attempt < max_retries - 1:
                        # 从错误信息中提取重试延迟时间
                        retry_delay = 30  # 默认30秒
                        if "retryDelay" in error_str:
                            try:
                                import re
                                delay_match = re.search(r"retryDelay.*?(\d+)s", error_str)
                                if delay_match:
                                    retry_delay = int(delay_match.group(1))
                            except:
                                pass

                        logger.info(f"检测到配额限制，等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                        continue
                    else:
                        logger.error("达到最大重试次数，配额限制无法解决")
                        raise e
                else:
                    # 其他错误，直接抛出
                    raise e

        raise Exception("LLM调用失败，已达到最大重试次数")

    def _get_api_key(self) -> str:
        """获取API密钥"""
        try:
            # 尝试从多个来源获取API密钥
            import os
            from pathlib import Path
            import yaml

            # 1. 从环境变量获取
            api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
            if api_key:
                return api_key

            # 2. 从secrets.yaml文件获取
            secrets_file = Path(__file__).parent.parent / "secrets.yaml"
            if secrets_file.exists():
                with open(secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            # 3. 从data_folder/secrets.yaml获取
            data_secrets_file = Path(__file__).parent.parent / "data_folder" / "secrets.yaml"
            if data_secrets_file.exists():
                with open(data_secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            logger.warning("未找到API密钥")
            return None

        except Exception as e:
            logger.error(f"获取API密钥时出错: {str(e)}")
            return None

    def _truncate_html_for_llm(self, html_content: str, max_chars: int = 40000) -> str:
        """智能截取和清理HTML内容以适应LLM token限制和API配额"""
        logger.info(f"开始处理HTML内容，原始长度: {len(html_content)} 字符")

        # 第一步：智能提取职位相关的HTML片段
        job_sections = self._extract_job_sections(html_content)

        if job_sections:
            # 保持所有职位片段，不限制数量
            combined_content = '\n'.join(job_sections)
            logger.info(f"提取到 {len(job_sections)} 个职位片段，合并后长度: {len(combined_content)} 字符")
        else:
            # 如果没有找到职位片段，使用原始内容
            combined_content = html_content
            logger.warning("未找到职位片段，使用原始HTML内容")

        # 第二步：清理HTML内容，移除不必要的元素
        cleaned_content = self._clean_html_for_llm(combined_content)
        logger.info(f"HTML清理后长度: {len(cleaned_content)} 字符")

        # 第三步：如果仍然超过限制，进行智能截取
        if len(cleaned_content) <= max_chars:
            return cleaned_content

        # 查找包含最多职位关键词的部分
        job_keywords = ['job', 'position', 'career', 'work', 'employment', 'base-search-card', 'job-card', 'easy-apply']
        best_start = 0
        best_score = 0

        for i in range(0, len(cleaned_content) - max_chars, 2000):
            chunk = cleaned_content[i:i + max_chars].lower()
            score = sum(chunk.count(keyword) for keyword in job_keywords)
            if score > best_score:
                best_score = score
                best_start = i

        truncated = cleaned_content[best_start:best_start + max_chars]
        logger.info(f"HTML内容最终截取: {len(cleaned_content)} -> {len(truncated)} 字符")
        return truncated

    def _extract_job_sections(self, html_content: str) -> List[str]:
        """提取HTML中的职位相关片段"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            job_sections = []
            
            # 1. 优先处理带注释标记的HTML
            main_job_sections = self._extract_main_job_sections(soup)
            if main_job_sections:
                logger.info(f"✅ 从主要职位列表提取到 {len(main_job_sections)} 个职位片段")
                job_sections.extend(main_job_sections)
            
            # 2. 如果主要职位列表不足，谨慎添加推荐职位
            if len(job_sections) < 10:
                recommendation_sections = self._extract_recommendation_sections(soup)
                if recommendation_sections:
                    logger.info(f"⚠️ 从推荐区域补充 {len(recommendation_sections)} 个职位片段")
                    job_sections.extend(recommendation_sections)
            
            # 3. 如果仍然不足，使用传统方法
            if len(job_sections) < 5:
                logger.info(f"当前只找到 {len(job_sections)} 个职位片段，使用传统方法")
                traditional_sections = self._extract_traditional_job_sections(soup)
                job_sections.extend(traditional_sections)

            # 去重（基于内容相似性）
            unique_sections = self._deduplicate_job_sections(job_sections)

            logger.info(f"总共提取到 {len(unique_sections)} 个有效职位片段")
            return unique_sections[:30]  # 限制到30个片段

        except Exception as e:
            logger.warning(f"提取职位片段失败: {str(e)}")
            return []
    
    def _extract_main_job_sections(self, soup) -> List[str]:
        """提取主要职位列表中的职位片段"""
        job_sections = []
        
        # 查找主要职位列表标记
        main_start_comment = soup.find(string=lambda text: isinstance(text, str) and 'MAIN_JOB_LIST_START' in text)
        main_end_comment = soup.find(string=lambda text: isinstance(text, str) and 'MAIN_JOB_LIST_END' in text)
        
        if main_start_comment and main_end_comment:
            logger.info("📍 找到主要职位列表标记，优先提取主要职位")
            
            # 获取标记之间的内容
            start_element = main_start_comment.parent
            end_element = main_end_comment.parent
            
            # 提取标记之间的所有职位元素
            current = start_element.next_sibling
            while current and current != end_element:
                if hasattr(current, 'find_all'):
                    # 在这个区域内查找职位卡片
                    job_cards = current.find_all(['div', 'li', 'article'], 
                                                attrs={'data-job-id': True}) or \
                               current.find_all(class_=lambda x: x and any(cls in str(x) for cls in 
                                               ['job-card', 'base-search-card', 'scaffold-layout__list-item']))
                    
                    for card in job_cards:
                        job_html = str(card)
                        if self._validate_job_section(job_html):
                            job_sections.append(job_html)
                
                current = current.next_sibling
        
        return job_sections
    
    def _extract_recommendation_sections(self, soup) -> List[str]:
        """提取推荐职位区域的职位片段（谨慎处理）"""
        job_sections = []
        
        # 查找推荐区域标记
        rec_start_comment = soup.find(string=lambda text: isinstance(text, str) and 'RECOMMENDATION_SECTION_START' in text)
        rec_end_comment = soup.find(string=lambda text: isinstance(text, str) and 'RECOMMENDATION_SECTION_END' in text)
        
        if rec_start_comment and rec_end_comment:
            logger.info("📍 找到推荐职位区域标记，谨慎提取推荐职位")
            
            # 获取标记之间的内容
            start_element = rec_start_comment.parent
            end_element = rec_end_comment.parent
            
            # 提取标记之间的职位元素（限制数量）
            current = start_element.next_sibling
            while current and current != end_element and len(job_sections) < 5:  # 限制推荐职位数量
                if hasattr(current, 'find_all'):
                    job_cards = current.find_all(['div', 'li', 'article'], 
                                                attrs={'data-job-id': True}) or \
                               current.find_all(class_=lambda x: x and any(cls in str(x) for cls in 
                                               ['job-card', 'base-search-card']))
                    
                    for card in job_cards:
                        if len(job_sections) >= 5:  # 限制推荐职位数量
                            break
                        job_html = str(card)
                        if self._validate_job_section(job_html):
                            job_sections.append(job_html)
                
                current = current.next_sibling
        
        return job_sections
    
    def _extract_traditional_job_sections(self, soup) -> List[str]:
        """使用传统方法提取职位片段（回退方案）"""
        job_sections = []
        
        # 定义职位卡片的可能选择器（按优先级排序）
        job_selectors = [
            'ul.OoooJEochOvWBisCtJXZlHzIbmfvuKmCDtNL li.scaffold-layout__list-item',  # 新的主要搜索结果
            '.scaffold-layout__list-item .job-card-job-posting-card-wrapper',  # 搜索结果中的职位卡片
            '[data-job-id]',  # 最准确的职位标识
            '.job-card-job-posting-card-wrapper',  # 职位卡片包装器
            '.base-search-card',
            '.job-card-container',
            '.jobs-search-results__list-item',
            '.job-card-list__item',
            '.job-search-card',
            '.jobs-search-card',
            '.artdeco-entity-lockup'
        ]

        for selector in job_selectors:
            elements = soup.select(selector)
            if elements:
                logger.info(f"使用传统选择器 {selector} 找到 {len(elements)} 个职位元素")

                for i, element in enumerate(elements):
                    job_html = str(element)
                    # 验证职位片段质量
                    if self._validate_job_section(job_html):
                        job_sections.append(job_html)
                        logger.debug(f"✅ 添加职位片段 {i+1}: {len(job_html)} 字符")
                    else:
                        logger.debug(f"跳过低质量职位片段 {i+1}")
                break  # 找到职位元素后就停止

        # 如果仍然不足，尝试其他方法
        if len(job_sections) < 5:
            additional_sections = self._extract_additional_job_sections(soup)
            job_sections.extend(additional_sections)
        
        return job_sections

    def _validate_job_section(self, job_html: str) -> bool:
        """验证职位片段的质量"""
        if len(job_html) < 50:  # 降低最小长度要求
            return False

        # 检查是否包含职位相关的关键信息
        job_html_lower = job_html.lower()
        # 扩展关键词列表，降低要求
        required_keywords = ['job', 'position', 'company', 'location', 'title', 'apply', 'hiring', 'work', 'career', 'role']
        found_keywords = sum(1 for keyword in required_keywords if keyword in job_html_lower)

        # 降低要求：至少包含1个关键词即可
        return found_keywords >= 1

    def _extract_additional_job_sections(self, soup) -> List[str]:
        """提取额外的职位片段"""
        additional_sections = []

        # 查找包含职位关键词的div
        all_divs = soup.find_all(['div', 'article', 'section'])
        for div in all_divs:
            div_text = div.get_text().lower()
            if any(keyword in div_text for keyword in ['easy apply', 'apply now', 'hiring', 'position']):
                job_html = str(div)
                if self._validate_job_section(job_html):
                    additional_sections.append(job_html)
                    if len(additional_sections) >= 10:  # 限制额外片段数量
                        break

        logger.info(f"额外找到 {len(additional_sections)} 个职位片段")
        return additional_sections

    def _deduplicate_job_sections(self, job_sections: List[str]) -> List[str]:
        """去除重复的职位片段"""
        unique_sections = []
        seen_hashes = set()

        for section in job_sections:
            # 使用内容的hash来检测重复
            section_hash = hash(section[:500])  # 使用前500字符计算hash
            if section_hash not in seen_hashes:
                seen_hashes.add(section_hash)
                unique_sections.append(section)

        logger.info(f"去重后保留 {len(unique_sections)} 个唯一职位片段")
        return unique_sections

    def _clean_html_for_llm(self, html_content: str) -> str:
        """激进清理HTML内容，移除不必要的元素以大幅减少token使用"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 移除所有不必要的标签
            tags_to_remove = ['script', 'style', 'noscript', 'iframe', 'embed', 'object', 'svg', 'img',
                             'nav', 'header', 'footer', 'aside', 'form', 'input', 'button', 'select', 'textarea']
            for tag in tags_to_remove:
                for element in soup.find_all(tag):
                    element.decompose()

            # 移除所有属性，只保留最关键的
            for element in soup.find_all():
                attrs_to_keep = ['data-job-id', 'href']  # 只保留最关键的属性
                attrs_to_remove = [attr for attr in element.attrs if attr not in attrs_to_keep]
                for attr in attrs_to_remove:
                    del element[attr]

            # 移除空元素和只包含空白的元素
            for element in soup.find_all():
                if not element.get_text(strip=True) and not element.find_all():
                    element.decompose()

            # 转换为文本并进行激进清理
            cleaned_html = str(soup)
            import re

            # 移除多余的HTML标签，只保留基本结构
            cleaned_html = re.sub(r'<(?!/?(?:div|span|a|p|h[1-6]|ul|li|strong|em)\b)[^>]*>', '', cleaned_html)

            # 合并多个空白字符
            cleaned_html = re.sub(r'\s+', ' ', cleaned_html)

            # 移除标签间的空白
            cleaned_html = re.sub(r'>\s+<', '><', cleaned_html)

            # 移除空行
            cleaned_html = re.sub(r'\n\s*\n', '\n', cleaned_html)

            return cleaned_html.strip()

        except Exception as e:
            logger.warning(f"HTML清理失败: {str(e)}")
            # 如果清理失败，至少进行基本的文本清理
            import re
            cleaned = re.sub(r'<[^>]+>', ' ', html_content)  # 移除所有HTML标签
            cleaned = re.sub(r'\s+', ' ', cleaned)  # 合并空白
            return cleaned.strip()[:40000]  # 限制长度




    


    def _trigger_lazy_loading_for_all_jobs(self, is_last_page=False):
        """智能滚动策略：逐个触发所有职位卡片的懒加载"""
        try:
            # 首先获取所有职位卡片元素
            job_cards = self.driver.find_elements(By.CSS_SELECTOR, ".scaffold-layout__list-item")
            logger.info(f"找到 {len(job_cards)} 个职位卡片，开始逐个触发懒加载...")

            # 逐个滚动到每个职位卡片
            for i, job_card in enumerate(job_cards):
                try:
                    # 滚动到职位卡片中心位置
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", job_card)

                    # 🔧 对页面底部的职位卡片增加更长的等待时间
                    if i >= len(job_cards) - 3:  # 最后3个职位卡片
                        wait_time = 2.5  # 增加到2.5秒，确保懒加载完成
                        logger.debug(f"职位卡片 {i+1} (页面底部) 滚动完成，等待 {wait_time}s 确保懒加载...")
                    else:
                        wait_time = 1.5  # 普通职位卡片1.5秒，比之前稍长
                        logger.debug(f"职位卡片 {i+1} 滚动完成，等待 {wait_time}s...")

                    time.sleep(wait_time)

                except Exception as e:
                    logger.debug(f"处理职位卡片 {i+1} 时出错: {e}")
                    continue

            # 🚨 关键修复：最后一页需要特殊处理，确保所有职位都被加载
            if is_last_page:
                logger.info("🔚 检测到最后一页，使用特殊滚动策略确保所有职位加载完成")

                # 🔧 策略1: 多次渐进式滚动，确保触发所有懒加载
                logger.info("📜 执行渐进式滚动，确保页面底部职位完全加载...")
                for scroll_step in range(3):
                    # 滚动到页面的不同位置，逐步触发懒加载
                    scroll_ratio = 0.7 + (scroll_step * 0.1)  # 70%, 80%, 90%
                    self.driver.execute_script(f"window.scrollTo(0, document.body.scrollHeight * {scroll_ratio});")
                    time.sleep(2)  # 每次滚动后等待2秒
                    logger.debug(f"渐进滚动步骤 {scroll_step + 1}/3 完成")

                # 🔧 策略2: 滚动到分页控件附近（但不超过）
                logger.info("🎯 滚动到分页控件附近，确保底部职位可见...")
                try:
                    # 查找分页控件
                    pagination_element = self.driver.find_element(By.CSS_SELECTOR, ".jobs-search-pagination")
                    # 滚动到分页控件上方一点的位置
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'start', behavior: 'smooth'}); window.scrollBy(0, -200);", pagination_element)
                    time.sleep(3)  # 等待懒加载完成
                except:
                    # 如果找不到分页控件，使用备用方法
                    logger.warning("⚠️ 未找到分页控件，使用备用滚动方法")
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.85);")
                    time.sleep(3)

                # 🔧 策略3: 重新检查并滚动到所有职位卡片
                logger.info("🔍 重新检查职位卡片数量...")
                updated_job_cards = self.driver.find_elements(By.CSS_SELECTOR, ".scaffold-layout__list-item")
                logger.info(f"更新后找到 {len(updated_job_cards)} 个职位卡片")

                # 如果发现了新的职位卡片，滚动到它们
                if len(updated_job_cards) > len(job_cards):
                    logger.info(f"🎉 发现了 {len(updated_job_cards) - len(job_cards)} 个新的职位卡片！")
                    for new_card in updated_job_cards[len(job_cards):]:
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", new_card)
                        time.sleep(1.5)

                # 最后滚动到最后一个职位卡片
                if updated_job_cards:
                    last_job_card = updated_job_cards[-1]
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", last_job_card)
                    time.sleep(2)

            else:
                # 非最后一页时，滚动到页面底部确保所有内容都被触发
                logger.info("滚动到页面底部以确保所有内容加载...")
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(3)

                # 🔧 额外验证：再次检查职位卡片是否完全加载
                logger.info("🔍 验证所有职位卡片是否完全加载...")
                time.sleep(2)  # 额外等待确保懒加载完成

            # 🔧 最终验证：检查职位卡片总数
            final_job_cards = self.driver.find_elements(By.CSS_SELECTOR, ".scaffold-layout__list-item")
            logger.info(f"🎯 滚动完成后最终检测到 {len(final_job_cards)} 个职位卡片")

            if len(final_job_cards) > len(job_cards):
                logger.info(f"✅ 成功触发了 {len(final_job_cards) - len(job_cards)} 个额外职位的懒加载！")

            # 回到顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)

        except Exception as e:
            logger.error(f"智能滚动触发懒加载失败: {e}")
            # 回退到简单滚动策略
            logger.info("回退到简单滚动策略...")
            for i in range(5):
                scroll_position = (i + 1) * (100 / 5)  # 分5次滚动
                self.driver.execute_script(f"window.scrollTo(0, document.body.scrollHeight * {scroll_position / 100});")
                time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)

    def _extract_job_id(self, job_url: str) -> str:
        """从URL提取职位ID"""
        try:
            if '/jobs/view/' in job_url:
                return job_url.split('/jobs/view/')[1].split('/')[0]
            return job_url.split('/')[-1].split('?')[0]
        except:
            return str(hash(job_url))
    
    def apply_to_job(self, job_info: Dict) -> bool:
        """申请职位"""
        try:
            job_id = job_info['job_id']
            
            # 检查是否已申请
            if job_id in self.applied_jobs:
                logger.info(f"职位 {job_info['title']} 已申请过，跳过")
                return False
            
            if not job_info['is_easy_apply']:
                logger.info(f"职位 {job_info['title']} 不支持Easy Apply，跳过")
                return False
            
            logger.info(f"开始申请职位: {job_info['title']} at {job_info['company']}")
            logger.info(f"职位URL: {job_info['url']}")
            logger.info(f"Easy Apply状态: {job_info['is_easy_apply']}")

            # 打开职位页面
            self.driver.get(job_info['url'])
            time.sleep(3)  # 增加等待时间确保页面完全加载

            # 等待页面加载完成
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                logger.info("职位页面加载完成")
            except TimeoutException:
                logger.warning("职位页面加载超时")
                return False
            
            # 查找并点击Easy Apply按钮 - 使用简化的选择器策略
            easy_apply_button = None
            easy_apply_selectors = [
                # 🎯 主要选择器：直接使用jobs-apply-button类（最有效）
                "//button[contains(@class, 'jobs-apply-button')]",
                # 🎯 备用选择器：包含Easy Apply文本
                "//button[contains(text(), 'Easy Apply')]",
                "//button[contains(., '轻松申请')]",
                # 🎯 最后备用：通过aria-label
                "//button[contains(@aria-label, 'Easy Apply')]",
            ]

            try:
                for selector in easy_apply_selectors:
                    try:
                        logger.info(f"尝试选择器: {selector}")
                        easy_apply_button = self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        if easy_apply_button:
                            logger.info(f"找到Easy Apply按钮，使用选择器: {selector}")
                            break
                    except TimeoutException:
                        continue

                if not easy_apply_button:
                    # 最后尝试：查找所有包含"Easy Apply"文本的按钮
                    logger.info("尝试查找所有包含Easy Apply文本的按钮...")
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for button in all_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.strip()
                            if "Easy Apply" in button_text or "轻松申请" in button_text:
                                easy_apply_button = button
                                logger.info(f"通过文本匹配找到Easy Apply按钮: {button_text}")
                                break

                if easy_apply_button:
                    # 记录按钮信息
                    button_text = easy_apply_button.text or easy_apply_button.get_attribute('aria-label') or 'Easy Apply'
                    button_class = easy_apply_button.get_attribute('class')
                    logger.info(f"找到Easy Apply按钮: '{button_text}' (class: {button_class})")

                    # 确保按钮可见和可点击
                    if not easy_apply_button.is_displayed():
                        logger.warning("Easy Apply按钮不可见，尝试滚动到按钮位置")
                        self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", easy_apply_button)
                        time.sleep(2)

                    if not easy_apply_button.is_enabled():
                        logger.error("Easy Apply按钮不可点击")
                        return False

                    # 多种点击方法尝试
                    click_success = False

                    # 方法1: 标准点击
                    try:
                        logger.info("尝试方法1: 标准点击")
                        easy_apply_button.click()
                        click_success = True
                        logger.info("方法1成功: 标准点击")
                    except Exception as e:
                        logger.warning(f"方法1失败: {str(e)}")

                    # 方法2: JavaScript点击
                    if not click_success:
                        try:
                            logger.info("尝试方法2: JavaScript点击")
                            self.driver.execute_script("arguments[0].click();", easy_apply_button)
                            click_success = True
                            logger.info("方法2成功: JavaScript点击")
                        except Exception as e:
                            logger.warning(f"方法2失败: {str(e)}")

                    # 方法3: ActionChains点击
                    if not click_success:
                        try:
                            from selenium.webdriver.common.action_chains import ActionChains
                            logger.info("尝试方法3: ActionChains点击")
                            actions = ActionChains(self.driver)
                            actions.move_to_element(easy_apply_button).click().perform()
                            click_success = True
                            logger.info("方法3成功: ActionChains点击")
                        except Exception as e:
                            logger.warning(f"方法3失败: {str(e)}")

                    # 方法4: 强制JavaScript点击
                    if not click_success:
                        try:
                            logger.info("尝试方法4: 强制JavaScript点击")
                            self.driver.execute_script("""
                                arguments[0].style.display = 'block';
                                arguments[0].style.visibility = 'visible';
                                arguments[0].disabled = false;
                                arguments[0].click();
                            """, easy_apply_button)
                            click_success = True
                            logger.info("方法4成功: 强制JavaScript点击")
                        except Exception as e:
                            logger.warning(f"方法4失败: {str(e)}")

                    if not click_success:
                        logger.error("所有点击方法都失败了")
                        return False

                    logger.info("Easy Apply按钮点击成功！")
                    time.sleep(3)  # 等待页面响应

                    # 检查页面是否发生变化
                    current_url = self.driver.current_url
                    logger.info(f"点击后当前URL: {current_url}")

                    # 等待申请弹窗或新页面加载
                    try:
                        # 等待申请相关元素出现
                        WebDriverWait(self.driver, 10).until(
                            EC.any_of(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test-modal]")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".jobs-easy-apply-modal")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".artdeco-modal")),
                                EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Next') or contains(text(), 'Submit') or contains(text(), 'Review')]"))
                            )
                        )
                        logger.info("申请弹窗或流程已加载")
                    except TimeoutException:
                        logger.warning("未检测到申请弹窗，可能页面加载较慢")
                        # 截图用于调试
                        try:
                            self.driver.save_screenshot(f"log/after_easy_apply_click_{int(time.time())}.png")
                            logger.info("已保存点击后的页面截图")
                        except Exception as e:
                            logger.warning(f"保存截图失败: {str(e)}")
                else:
                    logger.warning(f"未找到Easy Apply按钮: {job_info['title']}")
                    # 截图用于调试
                    try:
                        self.driver.save_screenshot(f"log/no_easy_apply_{int(time.time())}.png")
                        logger.info("已保存调试截图")
                    except Exception as e:
                        logger.warning(f"保存截图失败: {str(e)}")
                    return False

            except Exception as e:
                logger.error(f"点击Easy Apply按钮失败: {str(e)}")
                return False
            
            # 处理申请流程
            success = self._handle_application_process()
            
            if success:
                self.applied_jobs.add(job_id)
                logger.info(f"成功申请职位: {job_info['title']}")

                # 🔧 关闭申请成功通知窗口
                try:
                    logger.info("检查并关闭申请成功通知窗口...")
                    self._check_and_handle_application_success()
                except Exception as e:
                    logger.warning(f"关闭成功通知窗口失败: {str(e)}")

                # 随机延迟
                delay = random.randint(*self.config['linkedin']['delay_between_applications'])
                logger.info(f"等待 {delay} 秒后继续...")
                time.sleep(delay)
                
            return success
            
        except Exception as e:
            logger.error(f"申请职位失败 {job_info['title']}: {str(e)}")
            return False
    
    def _handle_application_process(self) -> bool:
        """处理申请流程"""
        try:
            max_steps = 10  # 增加最大步骤数
            current_step = 0

            logger.info("开始处理LinkedIn申请流程...")

            # 记录当前页面状态
            logger.info(f"申请流程开始时URL: {self.driver.current_url}")
            logger.info(f"申请流程开始时页面标题: {self.driver.title}")

            while current_step < max_steps:
                logger.info(f"申请流程步骤 {current_step + 1}/{max_steps}")
                time.sleep(0.5)  # 进一步减少等待时间

                # 记录当前页面状态
                logger.debug(f"步骤 {current_step + 1} - 当前URL: {self.driver.current_url}")

                # 🔧 优化：快速预检查页面状态，避免不必要的处理
                page_has_questions = self._quick_check_for_questions()
                page_has_modals = bool(self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-modal, .jobs-easy-apply-modal"))

                if page_has_modals:
                    logger.debug(f"检测到模态框")

                # 只有在检测到问题时才进行详细处理
                if page_has_questions and self._answer_application_questions():
                    logger.info("回答了申请问题，继续下一步")
                    current_step += 1
                    continue
                
                # 使用智能按钮检测
                button, button_type = self._find_application_button()

                if button and button_type == 'submit':
                    logger.info("找到提交按钮，正在提交申请...")
                    button.click()
                    time.sleep(2)  # 减少等待时间到2秒

                    # 检查是否申请成功并处理成功通知
                    if self._check_and_handle_application_success():
                        logger.info("申请成功提交并已处理通知")
                        return True
                    else:
                        logger.info("未找到成功消息，但申请可能已提交")
                        return True

                elif button and button_type in ['next', 'review']:
                    button_text = button.text or button.get_attribute('aria-label') or f'{button_type} button'
                    logger.info(f"点击{button_type}按钮: {button_text}")

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(0.5)  # 减少滚动等待时间

                    button.click()
                    current_step += 1
                    time.sleep(1.5)  # 减少等待时间到1.5秒

                else:
                    # 检查是否有错误或需要手动处理
                    logger.warning(f"申请流程步骤 {current_step + 1}: 未找到可点击的按钮")

                    # 尝试查找其他可能的按钮
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    visible_buttons = [btn for btn in all_buttons if btn.is_displayed() and btn.is_enabled()]

                    if visible_buttons:
                        logger.info(f"找到 {len(visible_buttons)} 个可见按钮:")
                        for i, btn in enumerate(visible_buttons[:5]):  # 显示前5个
                            btn_text = btn.text.strip() or btn.get_attribute('aria-label') or f'Button {i+1}'
                            btn_class = btn.get_attribute('class') or 'no-class'
                            logger.info(f"  按钮 {i+1}: '{btn_text}' (class: {btn_class[:50]})")
                    else:
                        logger.warning("页面上没有找到任何可见的按钮")

                    # 🔧 优化：只在调试模式下保存截图，避免耗时
                    if logger.level <= 10:  # DEBUG级别
                        try:
                            self.driver.save_screenshot(f"log/application_stuck_step_{current_step + 1}_{int(time.time())}.png")
                            logger.debug(f"已保存步骤 {current_step + 1} 的调试截图")
                        except Exception as e:
                            logger.debug(f"保存截图失败: {str(e)}")
                    else:
                        logger.info(f"跳过截图保存以提高速度（步骤 {current_step + 1}）")

                    break
            
            logger.warning("申请流程未能完成，可能需要手动处理")
            return False

        except Exception as e:
            logger.error(f"处理申请流程失败: {str(e)}")
            return False

    def _find_application_button(self) -> tuple:
        """智能查找申请流程中的按钮 - 优化版本

        Returns:
            (button_element, button_type) - button_type可以是'next', 'submit', 'review'
        """
        try:
            # 🔧 优化：先获取所有主要按钮，然后分析文本内容
            primary_buttons = self.driver.find_elements(
                By.CSS_SELECTOR,
                "button.artdeco-button--primary, button[aria-label*='Submit'], button[aria-label*='Continue'], button[aria-label*='Next']"
            )

            for button in primary_buttons:
                if not (button.is_displayed() and button.is_enabled()):
                    continue

                button_text = (button.text or button.get_attribute('aria-label') or '').lower()

                # 检查按钮类型
                if any(keyword in button_text for keyword in ['submit application', 'submit']):
                    logger.info(f"找到submit按钮: {button_text}")
                    return button, 'submit'
                elif any(keyword in button_text for keyword in ['review', '审核']):
                    logger.info(f"找到review按钮: {button_text}")
                    return button, 'review'
                elif any(keyword in button_text for keyword in ['next', 'continue', '下一步']):
                    logger.info(f"找到next按钮: {button_text}")
                    return button, 'next'

            # 🔧 备用方案：如果没找到主要按钮，快速检查常见选择器
            fallback_selectors = [
                ("button[aria-label='Submit application']", 'submit'),
                ("//button[contains(text(), 'Submit')]", 'submit'),
                ("//button[contains(text(), 'Next')]", 'next'),
                ("//button[contains(text(), 'Review')]", 'review'),
            ]

            for selector, button_type in fallback_selectors:
                try:
                    if selector.startswith('//'):
                        button = self.driver.find_element(By.XPATH, selector)
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if button.is_displayed() and button.is_enabled():
                        logger.info(f"找到{button_type}按钮: {button.text or button.get_attribute('aria-label')}")
                        return button, button_type
                except Exception:
                    continue

            return None, None

        except Exception as e:
            logger.warning(f"查找申请按钮失败: {str(e)}")
            return None, None

    def _quick_check_for_questions(self) -> bool:
        """快速检查页面是否有需要回答的问题"""
        try:
            # 快速检查常见的问题元素
            question_indicators = self.driver.find_elements(
                By.CSS_SELECTOR,
                "input[type='text']:not([value]), input[type='number']:not([value]), "
                "select:not([value]), input[type='radio']:not(:checked), "
                "input[type='checkbox']:not(:checked), textarea:not([value]), "
                "button[aria-haspopup='listbox']"
            )
            return len(question_indicators) > 0
        except Exception:
            return True  # 如果检查失败，假设有问题需要处理

    def _answer_application_questions(self) -> bool:
        """回答申请问题 - 优化版本，批量处理提高效率"""
        try:
            answered = False
            logger.info("开始处理LinkedIn申请问题...")

            # 🔧 优化：并行检查所有问题类型，减少DOM查询次数
            handlers = [
                self._handle_text_inputs,
                self._handle_radio_buttons,
                self._handle_select_dropdowns,
                self._handle_checkboxes,
                self._handle_custom_dropdowns,
                self._handle_file_uploads
            ]

            # 批量执行所有处理器
            for handler in handlers:
                try:
                    if handler():
                        answered = True
                        # 找到问题就立即处理，不等待其他类型
                        break
                except Exception as e:
                    logger.debug(f"处理器 {handler.__name__} 失败: {str(e)}")
                    continue

            if answered:
                logger.info("成功回答了申请问题")
                time.sleep(0.3)  # 进一步减少等待时间
            else:
                logger.debug("未找到需要回答的问题")

            return answered

        except Exception as e:
            logger.warning(f"回答申请问题失败: {str(e)}")
            return False

    def _handle_text_inputs(self) -> bool:
        """处理文本输入框"""
        try:
            answered = False
            text_inputs = self.driver.find_elements(
                By.CSS_SELECTOR,
                "input[type='text'], input[type='number'], input[type='email'], input[type='tel'], textarea"
            )

            for input_field in text_inputs:
                if input_field.is_displayed() and input_field.is_enabled():
                    # 跳过已填写的字段
                    current_value = input_field.get_attribute('value') or ''
                    if current_value.strip():
                        continue

                    placeholder = input_field.get_attribute('placeholder') or ''
                    label = self._get_field_label(input_field)
                    question_text = f"{placeholder} {label}".strip()

                    answer = self._get_answer_for_question(question_text)
                    if answer:
                        logger.info(f"回答文本问题: {question_text[:50]}... -> {answer}")
                        input_field.clear()
                        self._human_type(input_field, answer)
                        answered = True
                        time.sleep(1)

            return answered

        except Exception as e:
            logger.warning(f"处理文本输入框失败: {str(e)}")
            return False

    def _handle_radio_buttons(self) -> bool:
        """处理单选按钮组"""
        try:
            answered = False

            # 查找单选按钮组
            radio_groups = self.driver.find_elements(
                By.CSS_SELECTOR,
                "fieldset, .fb-radio-buttons, [role='radiogroup']"
            )

            for group in radio_groups:
                if group.is_displayed():
                    radios = group.find_elements(By.CSS_SELECTOR, "input[type='radio']")
                    if radios and not any(radio.is_selected() for radio in radios):
                        # 获取问题文本
                        question_text = self._get_question_text_for_group(group)

                        # 智能选择答案
                        selected_radio = self._select_best_radio_option(radios, question_text)
                        if selected_radio:
                            logger.info(f"选择单选按钮: {question_text[:50]}...")
                            self.driver.execute_script("arguments[0].click();", selected_radio)
                            answered = True
                            time.sleep(1)

            return answered

        except Exception as e:
            logger.warning(f"处理单选按钮失败: {str(e)}")
            return False

    def _handle_select_dropdowns(self) -> bool:
        """处理标准下拉选择框"""
        try:
            answered = False
            selects = self.driver.find_elements(By.CSS_SELECTOR, "select")

            for select in selects:
                if select.is_displayed() and select.is_enabled():
                    # 检查是否已选择
                    selected_option = select.find_element(By.CSS_SELECTOR, "option:checked")
                    if selected_option and selected_option.get_attribute('value'):
                        continue

                    options = select.find_elements(By.CSS_SELECTOR, "option")
                    if len(options) > 1:
                        # 获取问题文本
                        label = self._get_field_label(select)
                        question_text = label or select.get_attribute('name') or ''

                        # 智能选择选项
                        best_option = self._select_best_dropdown_option(options, question_text)
                        if best_option:
                            logger.info(f"选择下拉选项: {question_text[:50]}... -> {best_option.text}")
                            best_option.click()
                            answered = True
                            time.sleep(1)

            return answered

        except Exception as e:
            logger.warning(f"处理下拉选择框失败: {str(e)}")
            return False

    def _handle_checkboxes(self) -> bool:
        """处理复选框"""
        try:
            answered = False
            checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")

            for checkbox in checkboxes:
                if checkbox.is_displayed() and checkbox.is_enabled() and not checkbox.is_selected():
                    label = self._get_field_label(checkbox)
                    question_text = label or checkbox.get_attribute('name') or ''

                    # 根据问题内容决定是否勾选
                    should_check = self._should_check_checkbox(question_text)
                    if should_check:
                        logger.info(f"勾选复选框: {question_text[:50]}...")
                        checkbox.click()
                        answered = True
                        time.sleep(1)

            return answered

        except Exception as e:
            logger.warning(f"处理复选框失败: {str(e)}")
            return False

    def _handle_custom_dropdowns(self) -> bool:
        """处理LinkedIn自定义下拉框（如截图中的选择一项）"""
        try:
            answered = False

            # LinkedIn 2024年常见的自定义下拉框选择器
            custom_dropdown_selectors = [
                "button[aria-haspopup='listbox']",
                ".artdeco-dropdown__trigger",
                "[data-test-id*='dropdown']",
                "button[role='combobox']",
                ".fb-dropdown-trigger",
                "button:contains('选择一项')",
                "button:contains('Select an option')",
                "button:contains('Choose')"
            ]

            # 🔧 优化：先快速检查是否有下拉框，避免逐个尝试选择器
            all_dropdowns = []
            for selector in custom_dropdown_selectors[:4]:  # 只检查前4个最常用的选择器
                try:
                    dropdowns = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    all_dropdowns.extend(dropdowns)
                except Exception:
                    continue

            # 处理找到的下拉框
            for dropdown in all_dropdowns:
                try:
                    if dropdown.is_displayed() and dropdown.is_enabled():
                        # 检查是否已选择（按钮文本不是默认值）
                        button_text = dropdown.text.strip()
                        if button_text in ['选择一项', 'Select an option', 'Choose', '']:
                            logger.info(f"点击自定义下拉框: {button_text}")
                            dropdown.click()
                            time.sleep(1)  # 减少等待时间

                            # 查找并选择选项
                            if self._select_from_custom_dropdown():
                                answered = True
                                time.sleep(0.5)  # 减少等待时间
                                break  # 找到一个就够了，避免重复处理
                except Exception:
                    continue

            return answered

        except Exception as e:
            logger.warning(f"处理自定义下拉框失败: {str(e)}")
            return False

    def _handle_file_uploads(self) -> bool:
        """处理文件上传（简历等）"""
        try:
            answered = False
            file_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='file']")

            for file_input in file_inputs:
                if file_input.is_displayed():
                    # 这里可以上传默认简历文件
                    # 暂时跳过文件上传，避免复杂性
                    logger.info("检测到文件上传字段，暂时跳过")

            return answered

        except Exception as e:
            logger.warning(f"处理文件上传失败: {str(e)}")
            return False

    def _get_field_label(self, input_field) -> str:
        """获取输入框的标签"""
        try:
            # 尝试多种方式获取标签
            label_id = input_field.get_attribute('aria-labelledby')
            if label_id:
                label_element = self.driver.find_element(By.ID, label_id)
                return label_element.text
            
            # 查找相邻的label元素
            parent = input_field.find_element(By.XPATH, '..')
            label = parent.find_element(By.CSS_SELECTOR, 'label')
            return label.text
            
        except:
            return ''
    
    def _get_question_text_for_group(self, group_element) -> str:
        """获取问题组的文本"""
        try:
            # 尝试多种方式获取问题文本
            legend = group_element.find_element(By.CSS_SELECTOR, "legend")
            if legend:
                return legend.text
        except:
            pass

        try:
            label = group_element.find_element(By.CSS_SELECTOR, "label")
            if label:
                return label.text
        except:
            pass

        return group_element.text[:100] if group_element.text else ''

    def _select_best_radio_option(self, radios, question_text: str):
        """智能选择最佳单选选项"""
        try:
            question_lower = question_text.lower()

            # 获取所有选项的文本
            options_with_elements = []
            for radio in radios:
                try:
                    label = self._get_radio_label(radio)
                    options_with_elements.append((radio, label.lower()))
                except:
                    options_with_elements.append((radio, ''))

            # 根据问题类型智能选择
            if 'authorized' in question_lower or 'work authorization' in question_lower:
                # 工作授权问题 - 选择"是"
                for radio, label in options_with_elements:
                    if any(word in label for word in ['yes', '是', 'authorized', '有']):
                        return radio

            elif 'sponsorship' in question_lower or '赞助' in question_lower:
                # 赞助问题 - 选择"否"
                for radio, label in options_with_elements:
                    if any(word in label for word in ['no', '否', '不需要', 'not required']):
                        return radio

            elif 'relocate' in question_lower or '搬迁' in question_lower:
                # 搬迁问题 - 选择"是"
                for radio, label in options_with_elements:
                    if any(word in label for word in ['yes', '是', 'willing', '愿意']):
                        return radio

            # 默认选择第一个选项
            return radios[0] if radios else None

        except Exception as e:
            logger.warning(f"选择单选选项失败: {str(e)}")
            return radios[0] if radios else None

    def _get_radio_label(self, radio_element):
        """获取单选按钮的标签"""
        try:
            # 方法1: 通过for属性查找label
            radio_id = radio_element.get_attribute('id')
            if radio_id:
                label = self.driver.find_element(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                return label.text
        except:
            pass

        try:
            # 方法2: 查找父元素中的label
            parent = radio_element.find_element(By.XPATH, '..')
            label = parent.find_element(By.CSS_SELECTOR, 'label')
            return label.text
        except:
            pass

        try:
            # 方法3: 查找相邻的文本
            return radio_element.find_element(By.XPATH, 'following-sibling::*').text
        except:
            pass

        return ''

    def _select_best_dropdown_option(self, options, question_text: str):
        """智能选择最佳下拉选项"""
        try:
            question_lower = question_text.lower()

            # 跳过空选项
            valid_options = [opt for opt in options if opt.get_attribute('value') and opt.text.strip()]

            if not valid_options:
                return None

            # 根据问题类型智能选择
            if 'experience' in question_lower or '经验' in question_lower:
                # 经验年数问题
                for option in valid_options:
                    if any(year in option.text for year in ['3', '2-3', '3-5']):
                        return option

            elif 'education' in question_lower or '学历' in question_lower:
                # 学历问题
                for option in valid_options:
                    if any(edu in option.text.lower() for edu in ['bachelor', 'degree', '学士', '本科']):
                        return option

            # 默认选择第一个有效选项
            return valid_options[0]

        except Exception as e:
            logger.warning(f"选择下拉选项失败: {str(e)}")
            return options[1] if len(options) > 1 else None

    def _should_check_checkbox(self, question_text: str) -> bool:
        """判断是否应该勾选复选框"""
        question_lower = question_text.lower()

        # 通常不勾选的情况
        negative_keywords = ['newsletter', 'marketing', 'promotional', '营销', '推广']
        if any(keyword in question_lower for keyword in negative_keywords):
            return False

        # 通常勾选的情况
        positive_keywords = ['terms', 'conditions', 'privacy', 'agree', '同意', '条款']
        if any(keyword in question_lower for keyword in positive_keywords):
            return True

        # 默认不勾选
        return False

    def _select_from_custom_dropdown(self) -> bool:
        """从自定义下拉框中选择选项 - 优化版本"""
        try:
            time.sleep(0.5)  # 减少等待时间

            # LinkedIn常见的下拉选项选择器 - 按优先级排序
            option_selectors = [
                "[role='option']",  # 最常见
                ".artdeco-dropdown__item",
                "ul li button",
                "ul li a"
            ]

            # 🔧 优化：先收集所有选项，再选择
            all_options = []
            for selector in option_selectors:
                try:
                    options = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    all_options.extend(options)
                except Exception:
                    continue

            # 选择第一个可见的选项
            for option in all_options:
                try:
                    if option.is_displayed() and option.is_enabled():
                        option_text = option.text.strip()[:30]
                        logger.info(f"选择自定义下拉选项: {option_text}...")
                        option.click()
                        return True
                except Exception:
                    continue

            return False

        except Exception as e:
            logger.warning(f"从自定义下拉框选择失败: {str(e)}")
            return False

    def _get_answer_for_question(self, question_text: str) -> str:
        """根据问题文本获取答案 - 增强版本，支持用户设置文件"""
        question_lower = question_text.lower()

        # 获取默认答案配置，支持新旧配置格式
        if 'application' in self.config.get('linkedin', {}):
            # 新格式：linkedin.application.default_answers
            default_answers = self.config['linkedin']['application'].get('default_answers', {})
        else:
            # 旧格式：linkedin.default_answers
            default_answers = self.config.get('linkedin', {}).get('default_answers', {})

        # 经验年数问题
        if any(keyword in question_lower for keyword in ['experience', '经验', 'years', '年']):
            return default_answers.get('years_experience', '15')

        # 搬迁问题
        elif any(keyword in question_lower for keyword in ['relocate', '搬迁', 'move', '迁移']):
            return default_answers.get('willing_to_relocate', 'Yes')

        # 工作授权问题
        elif any(keyword in question_lower for keyword in ['authorized', 'work authorization', '工作授权', 'eligible']):
            return default_answers.get('authorized_to_work', 'Yes')

        # 赞助问题
        elif any(keyword in question_lower for keyword in ['sponsorship', '赞助', 'visa', '签证']):
            return default_answers.get('require_sponsorship', 'No')

        # 联系方式
        elif any(keyword in question_lower for keyword in ['phone', '电话', 'mobile', '手机']):
            return default_answers.get('phone', '1234567890')
        elif any(keyword in question_lower for keyword in ['email', '邮箱', '@']):
            return default_answers.get('email', '<EMAIL>')

        # 薪资期望
        elif any(keyword in question_lower for keyword in ['salary', '薪资', 'compensation', '薪酬', 'expected']):
            return default_answers.get('expected_salary', 'Negotiable面议')

        # 开始时间
        elif any(keyword in question_lower for keyword in ['start', '开始', 'available', '可入职', 'when']):
            return default_answers.get('start_date', 'Immediately立即')

        return ''

    def _check_and_handle_application_success(self) -> bool:
        """检查申请是否成功并处理成功通知窗口"""
        try:
            # 等待成功消息出现
            time.sleep(3)

            # 多种成功消息的选择器
            success_selectors = [
                "//*[contains(text(), 'Application sent')]",
                "//*[contains(text(), 'Your application was sent')]",
                "//*[contains(text(), 'Application submitted')]",
                "//*[contains(text(), '申请已发送')]",
                "//*[contains(text(), '申请已提交')]",
                "//*[contains(text(), 'Successfully applied')]",
                "//h2[contains(text(), 'Application sent')]",
                "//div[contains(@class, 'success') and contains(text(), 'sent')]"
            ]

            success_found = False
            for selector in success_selectors:
                try:
                    success_element = self.driver.find_element(By.XPATH, selector)
                    if success_element.is_displayed():
                        logger.info(f"检测到申请成功消息: {success_element.text[:50]}...")
                        success_found = True
                        break
                except NoSuchElementException:
                    continue

            if success_found:
                # 处理成功通知弹窗
                self._close_success_notification()
                return True

            # 如果没有找到明确的成功消息，检查是否有其他成功指示器
            return self._check_alternative_success_indicators()

        except Exception as e:
            logger.warning(f"检查申请成功状态失败: {str(e)}")
            return False

    def _close_success_notification(self):
        """关闭申请成功通知窗口"""
        try:
            logger.info("尝试关闭申请成功通知窗口...")

            # 等待通知窗口完全加载
            time.sleep(2)

            # 多种关闭按钮的选择器 - 优化顺序，最常见的放前面
            close_selectors = [
                # 🎯 LinkedIn申请成功弹窗的关闭按钮（最常见）
                "button[aria-label='Dismiss']",
                "button[aria-label='Close']",
                ".artdeco-modal__dismiss",

                # 🎯 通用X关闭按钮
                "button[data-control-name='overlay.close_overlay']",
                "button.artdeco-modal__dismiss",
                ".artdeco-toast-item__dismiss",

                # 🎯 包含X符号的按钮
                "//button[contains(text(), '×')]",
                "//button[contains(text(), '✕')]",
                "//button[contains(@aria-label, 'close')]",
                "//button[contains(@aria-label, 'Close')]",
                "//button[contains(@aria-label, 'Dismiss')]",

                # 🎯 确定/OK按钮
                "//button[contains(text(), 'OK')]",
                "//button[contains(text(), '确定')]",
                "//button[contains(text(), 'Got it')]",
                "//button[contains(text(), '知道了')]",

                # 🎯 继续/完成按钮
                "//button[contains(text(), 'Continue')]",
                "//button[contains(text(), '继续')]",
                "//button[contains(text(), 'Done')]",
                "//button[contains(text(), '完成')]",

                # 🎯 LinkedIn特定选择器
                ".jobs-apply-success-modal__dismiss",
                ".application-success-modal .artdeco-button",
                "[data-test-id*='success-modal'] button",
                "[data-test-id='close-button']"
            ]

            # 尝试点击关闭按钮
            for selector in close_selectors:
                try:
                    # 判断是XPath还是CSS选择器
                    if selector.startswith('//'):
                        close_buttons = self.driver.find_elements(By.XPATH, selector)
                    else:
                        close_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for button in close_buttons:
                        if button.is_displayed() and button.is_enabled():
                            logger.info(f"找到并点击关闭按钮: {selector}")
                            button.click()
                            time.sleep(2)
                            logger.info("成功关闭申请成功通知窗口")
                            return True
                except Exception as e:
                    logger.debug(f"选择器 {selector} 未找到关闭按钮: {str(e)}")
                    continue

            # 如果没有找到关闭按钮，尝试按ESC键
            try:
                logger.info("尝试按ESC键关闭通知")
                from selenium.webdriver.common.keys import Keys
                self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                time.sleep(2)
            except Exception:
                pass

            # 最后尝试点击模态框外部区域
            try:
                logger.info("尝试点击模态框外部区域")
                overlay = self.driver.find_element(By.CSS_SELECTOR, ".artdeco-modal__overlay")
                if overlay.is_displayed():
                    overlay.click()
                    time.sleep(2)
            except Exception:
                pass

            logger.info("成功通知窗口处理完成")
            return True

        except Exception as e:
            logger.warning(f"关闭成功通知窗口失败: {str(e)}")
            return False

    def _check_alternative_success_indicators(self) -> bool:
        """检查其他成功指示器"""
        try:
            # 检查URL变化（通常申请成功后会跳转）
            current_url = self.driver.current_url
            if 'applied' in current_url or 'success' in current_url:
                logger.info("通过URL变化检测到申请成功")
                return True

            # 检查页面标题变化
            page_title = self.driver.title
            if any(keyword in page_title.lower() for keyword in ['applied', 'success', 'sent']):
                logger.info("通过页面标题检测到申请成功")
                return True

            # 检查是否出现了"查看申请"或类似按钮
            view_application_selectors = [
                "//button[contains(text(), 'View application')]",
                "//button[contains(text(), '查看申请')]",
                "//a[contains(text(), 'View application')]",
                "//a[contains(text(), '查看申请')]"
            ]

            for selector in view_application_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        logger.info("通过'查看申请'按钮检测到申请成功")
                        return True
                except NoSuchElementException:
                    continue

            return False

        except Exception as e:
            logger.warning(f"检查其他成功指示器失败: {str(e)}")
            return False

    def _human_type(self, element, text: str):
        """模拟人类打字"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
    
    def batch_apply_jobs(self, max_applications: int = None) -> Dict:
        """批量申请职位"""
        if not max_applications:
            max_applications = self.config['linkedin']['max_applications_per_day']
        
        results = {
            'total_found': 0,
            'total_applied': 0,
            'successful_applications': [],
            'failed_applications': []
        }
        
        try:
            # 搜索职位
            for keyword in self.config['linkedin']['search_keywords']:
                if results['total_applied'] >= max_applications:
                    break
                    
                jobs = self.search_jobs(keyword)
                results['total_found'] += len(jobs)
                
                for job in jobs:
                    if results['total_applied'] >= max_applications:
                        break
                    
                    if self.apply_to_job(job):
                        results['total_applied'] += 1
                        results['successful_applications'].append(job)
                    else:
                        results['failed_applications'].append(job)
            
            logger.info(f"批量申请完成: 找到 {results['total_found']} 个职位，成功申请 {results['total_applied']} 个")
            return results
            
        except Exception as e:
            logger.error(f"批量申请失败: {str(e)}")
            return results
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                # 如果使用了Undetected ChromeDriver管理器，使用其quit方法
                if hasattr(self, '_chrome_manager') and self._chrome_manager:
                    self._chrome_manager.quit()
                else:
                    self.driver.quit()
                logger.info("浏览器已关闭")
            except Exception as e:
                logger.warning(f"关闭浏览器时出错: {str(e)}")
            finally:
                self.driver = None
                self.wait = None
                self.is_logged_in = False
                if hasattr(self, '_chrome_manager'):
                    self._chrome_manager = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# 使用示例
if __name__ == "__main__":
    # 创建配置文件示例
    config = {
        'linkedin': {
            'email': '<EMAIL>',
            'password': 'your_password',
            'search_keywords': ['python developer', 'software engineer', 'data scientist'],
            'location': 'United States',
            'max_applications_per_day': 20,
            'delay_between_applications': [30, 60],
            'auto_answer_questions': True,
            'default_answers': {
                'years_experience': '3',
                'willing_to_relocate': 'Yes',
                'authorized_to_work': 'Yes',
                'require_sponsorship': 'No'
            }
        },
        'selenium': {
            'headless': False,
            'implicit_wait': 10,
            'page_load_timeout': 30
        }
    }
    
    # 保存配置文件
    with open('linkedin_config.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    # 使用自动化工具
    with LinkedInAutomation('linkedin_config.yaml') as linkedin:
        linkedin.setup_driver()
        
        if linkedin.login():
            results = linkedin.batch_apply_jobs(max_applications=10)
            print(f"申请结果: {results}")